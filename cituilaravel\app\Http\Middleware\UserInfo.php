<?php
declare(strict_types=1);

namespace App\Http\Middleware;
use App\Exceptions\MyException;
use App\Models\User\User;
use App\Service\User\Auth\LoginService;
use Closure;
use Illuminate\Http\Request;
class UserInfo
{
    protected User $userModel;
    public function __construct(User $user)
    {
        $this->userModel = $user;
    }
    public function handle(Request $request, Closure $next)
    {
        //后置中间件
        $response = $next($request);
        $uri = request()->getRequestUri();
        if( strpos($uri,'api/loginDouyin') === false
            &&
            strpos($uri,'api/createAccountDouyin') === false
            &&
            strpos($uri,'api/loginWx') === false
            &&
            strpos($uri,'api/createAccountWx') === false
            &&
            strpos($uri,'api/loginWxApp') === false
            &&
            strpos($uri,'api/createAccountWxApp') === false
            &&
            strpos($uri,'api/regH5') === false
            &&
            strpos($uri,'api/regPc') === false
            &&
            strpos($uri,'api/loginH5') === false
            &&
            strpos($uri,'api/loginPc') === false
            &&
            strpos($uri,'api/user/bindPc') === false
            &&
            strpos($uri,'api/user/bind') === false
            &&
            strpos($uri,'api/user/detail') === false
            &&
            strpos($uri,'api/user/center') === false
        ){
            return $response;
        }
        if($request->user){
            $uid = ($request->user)['id'];
            $fields = ['*'];
            $user_info = $this->userModel::where('id',$uid)->select($fields)->first();
            if(!$user_info){
                throw new MyException("会员不存在",500);
            }
            $user_info = $user_info->toArray();
            if((int)$user_info['status'] == 0){
                throw new MyException("会员已禁用",500);
            }
            if(!is_array($request->user)){
                $request->user = $request->user->toArray();
            }

            $user_info = array_merge($request->user,$user_info);
            $check = (request()->system)['check'];
            if($check == 1){
                //审核版本 默认终身会员
                $user_info['is_fvip'] = 1;
                $user_info['k1'] = 1;
                $user_info['k2'] = 1;
                $user_info['k3'] = 1;
                $user_info['k4'] = 1;
                $user_info['deng'] = 1;
                $user_info['ispay'] = 1;
                $user_info['endtime'] = "2099-12-31";
            }else{
                if($user_info['istest']==1){
                    //审核期间注册的测试账户 全部过期
                    $user_info['ispay'] = 0;
                    $user_info['is_fvip'] = 0;
                    $user_info['endtime'] = "2000-12-31";
                }
            }

            if($user_info['is_fvip']==1){
                $user_info['ispay'] = 1;
                $user_info['endtime'] = "2099-12-31";
            }else{
                if($user_info['ispay']==1){
                    if(strtotime($user_info['endtime'])<=time()){
                        $user_info['ispay'] = 0;
                        $user_info['k1'] = 0;
                        $user_info['k2'] = 0;
                        $user_info['k3'] = 0;
                        $user_info['k4'] = 0;
                        $user_info['deng'] = 0;
                    }
                }
            }

            if($user_info['is_fvip']==1){
                $user_info['is_fvip'] = 1;
            }else{
                $user_info['is_fvip'] = 0;
                if($user_info['ispay']!=1){
                    $user_info['ispay'] = 0;
                }
            }

            if($user_info['k1'] != 1){
                $user_info['k1'] = 0;
            }
            if($user_info['k2'] != 1){
                $user_info['k2'] = 0;
            }
            if($user_info['k3'] != 1){
                $user_info['k3'] = 0;
            }
            if($user_info['k4'] != 1){
                $user_info['k4'] = 0;
            }
            if($user_info['deng'] != 1){
                $user_info['deng'] = 0;
            }

            if(!empty($user_info['endtime'])){
                if($time = strtotime($user_info['endtime'])){
                    $user_info['endtime'] = date('Y-m-d',$time);
                }
            }

            if(isset($user_info['adimg'])){
                $user_info['adimg'] = uploadFilePath($user_info['adimg']);
            }

        }else{
            $user_info = [];
        }
        if(is_array($request->user)){
            $request->user = json_encode($request->user,JSON_UNESCAPED_UNICODE,JSON_UNESCAPED_SLASHES);
        }
        if(is_array($user_info)){
            $user_info = json_encode($user_info,JSON_UNESCAPED_UNICODE,JSON_UNESCAPED_SLASHES);
        }

        if($user_info && $request->user) {
            $content = $response->getContent();
            $content = str_replace($request->user, $user_info, $content);
            $response->setContent($content);
        }
        return $response;
    }
}

<?php
declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Wap\Index;

use App\Http\Controllers\Api\Controller;
use App\Service\Index\IndexService;

class IndexController extends Controller
{
    protected IndexService $indexService;
    public function __construct(IndexService $indexService)
    {
        $this->indexService = $indexService;
        parent::__construct();
    }

    public function index(){
        return $this->apiSuccess($this->indexService->index());
    }

    public function gameList(){
        return $this->apiSuccess($this->indexService->gameList());
    }

    public function appDownload(){
        return $this->apiSuccess($this->indexService->appDownload());
    }

    public function gameDownload(){
        return $this->apiSuccess($this->indexService->gameDownload());
    }


    public function moneyAndAn(){
        return $this->apiSuccess($this->indexService->moneyAndAn());
    }

    public function getIcon(){
        return $this->apiSuccess($this->indexService->getIcon());
    }

    public function getOdds(){
        return $this->apiSuccess($this->indexService->getOdds());
    }

    public function getProtocol(){
        return $this->apiSuccess($this->indexService->getProtocol());
    }

    public function selectMoney(){
        $user = request()->attributes->get('user');
        return $this->apiSuccess($this->indexService->selectMoney($user));
    }

    public function getServices(){
        return $this->apiSuccess($this->indexService->getServices());
    }

}

<?php

namespace App\Listeners;

use App\Events\AuditStatusChanged;
use App\Services\Citui\AuditCallbackService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class HandleAuditStatusChange implements ShouldQueue
{
    use InteractsWithQueue;

    protected AuditCallbackService $callbackService;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(AuditCallbackService $callbackService)
    {
        $this->callbackService = $callbackService;
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\AuditStatusChanged  $event
     * @return void
     */
    public function handle(AuditStatusChanged $event)
    {
        try {
            // 只处理审核完成的状态变化
            if (in_array($event->newStatus, ['passed', 'rejected', 'timeout'])) {
                $this->callbackService->handleAuditCallback($event->audit->audit_id);
                
                Log::info('审核状态变化处理完成', [
                    'audit_id' => $event->audit->audit_id,
                    'old_status' => $event->oldStatus,
                    'new_status' => $event->newStatus,
                    'operator_id' => $event->operatorId
                ]);
            }
            
        } catch (\Exception $e) {
            Log::error('处理审核状态变化失败', [
                'audit_id' => $event->audit->audit_id,
                'error' => $e->getMessage()
            ]);
            
            // 重新抛出异常以触发队列重试
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \App\Events\AuditStatusChanged  $event
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(AuditStatusChanged $event, $exception)
    {
        Log::error('审核状态变化处理失败（最终失败）', [
            'audit_id' => $event->audit->audit_id,
            'old_status' => $event->oldStatus,
            'new_status' => $event->newStatus,
            'error' => $exception->getMessage()
        ]);
    }
}
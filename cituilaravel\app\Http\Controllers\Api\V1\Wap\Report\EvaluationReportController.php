<?php
declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Wap\Report;

use App\Http\Controllers\Api\Controller;
use App\Service\Report\EvaluationReportService;
use Illuminate\Http\JsonResponse;

class EvaluationReportController extends Controller
{
    protected EvaluationReportService $evaluationReportService;

    public function __construct(EvaluationReportService $evaluationReportService)
    {
        $this->evaluationReportService = $evaluationReportService;
        parent::__construct();
    }

    /**
     * 提交评测报告
     *
     * @return JsonResponse
     */
    public function submit(): JsonResponse
    {

        $result = $this->evaluationReportService->submitReport();
        return $this->apiSuccess($result, 200, '评测报告提交成功');

    }

    /**
     * 获取评测报告列表
     *
     * @return JsonResponse
     */
    public function getEvaluationList(): JsonResponse
    {
        $result = $this->evaluationReportService->getEvaluationList();
        return $this->apiSuccess($result);
    }
}

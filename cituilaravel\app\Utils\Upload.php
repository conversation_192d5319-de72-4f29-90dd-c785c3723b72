<?php
declare(strict_types=1);

namespace App\Utils;

use App\Exceptions\MyException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Upload
{
    protected $arrAllowExtension = [];

    protected Request $request;
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function upload(string $type='')
    {
        if(empty($type)){
            $type = 'image';
        }
        $this->arrAllowExtension = config('jk.allowExtension.'.$type);

        $obFile = $this->request->file('file');
        if(!$obFile)
        {
            throw new MyException("请选择文件");
        }

        $sOldName   = $obFile->getClientOriginalName();//原名

        $sFileType  = $obFile->getClientMimeType(); //文件类型

        $sExtensionName = strtolower($obFile->getClientOriginalExtension());//扩展名

        $sPath      = $obFile->getRealPath();    //文件存储的位置路径

        //验证文件大小不超过2MB
        if($obFile->getSize() > 2 * 1024 * 1024)
        {
            throw new MyException("文件大小不能超过2MB");
        }

        if (!in_array($sExtensionName,$this->arrAllowExtension) )
        {
            throw new MyException("文件格式不支持");
        }

        //组装文件存储的位置和自定义文件名
        $sFileName = 'upload/image/' . date('Ymd') . '/' . $this->saveFileName() . '.' .$sExtensionName;

        $bRet = Storage::disk('upload')->put($sFileName,file_get_contents($sPath));

        if (!$bRet)
        {
            throw new MyException("上传失败");
        }
        return ['url'=>uploadFilePathNoPre($sFileName),'path'=>'/'.$sFileName];
    }

    public function saveFileName(): string
    {
        return time().mt_rand(1000,9999).generateRandomString(4);
    }

    public function delete()
    {
       $path = $this->request->input('path');
       if(empty($path)) {
           throw new MyException("文件路径不能为空");
       }
       return $this->deleteFile($path);
    }

    public function deleteFile(string $path='')
    {
        if(empty($path)) {
            throw new MyException("文件路径不能为空");
        }

        // 使用 uploadFilePathNoPre 函数处理路径，去除URL前缀
        $cleanPath = uploadFilePathNoPre($path, 2); // type=2 表示去除前缀

        // 确保路径以斜杠开头
        if(!Str::startsWith($cleanPath,'/')){
            $cleanPath = '/'.$cleanPath;
        }

        $fullPath = public_path($cleanPath);

        // 检查文件是否存在
        if(!file_exists($fullPath)) {
            throw new MyException("文件不存在");
        }

        // 删除文件
        $result = @unlink($fullPath);

        if(!$result) {
            throw new MyException("文件删除失败");
        }

        return ['message' => '文件删除成功', 'path' => $cleanPath];
    }



}

<?php
namespace App\Console\Commands;

use App\Utils\BankUtil;
use App\Models\Card\Card;
use Illuminate\Support\Str;
use App\Jobs\SSEDataDealJob;
use App\Jobs\GoodsDataDealJob;
use Illuminate\Console\Command;
use App\Service\User\UserService;
use App\Service\User\Auth\LoginService;
use Illuminate\Support\Facades\DB;
use App\Service\Index\IndexService;
use App\Utils\Tools;

class TestNumberZeroCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    public function handle(UserService $userService,LoginService $loginService)
    {
        /* $arr = $userService->generateKsTestSign();
        echo $arr['trans_id'];
        echo "\n";
        echo $arr['sign']; */
        $a = $loginService->test_token('***********:21zl8ynvk+Im+JnY/q3QJ05vsRd8wf8OWapn5WFYabnrKLy5ZLzhQMahTYVK8OPz');
        var_dump($a);
    }

}

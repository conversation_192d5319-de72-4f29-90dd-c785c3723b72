-- =============================================
-- 次推应用简化数据库建表SQL脚本
-- 创建时间: 2025-01-08
-- 版本: 1.0.0
-- 描述: 次推应用的完整数据库表结构，无外键依赖，无触发器
-- =============================================

-- 设置字符集和存储引擎
SET NAMES utf8mb4;

-- 创建数据库（如果不存在）
-- CREATE DATABASE IF NOT EXISTS citui DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE citui;

-- =============================================
-- 管理员用户表
-- =============================================
CREATE TABLE ct_admin_users (
    admin_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '管理员ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱地址',
    phone VARCHAR(20) UNIQUE COMMENT '手机号码',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    role VARCHAR(50) DEFAULT 'admin' COMMENT '角色',
    permissions JSON COMMENT '权限列表',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户表';

-- =============================================
-- 普通用户表
-- =============================================
CREATE TABLE ct_users (
    user_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    phone VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号码',
    password_hash VARCHAR(255) COMMENT '密码哈希',
    nickname VARCHAR(100) COMMENT '用户昵称',
    real_name VARCHAR(100) COMMENT '真实姓名',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    gender ENUM('male', 'female', 'unknown') DEFAULT 'unknown' COMMENT '性别',
    birthday DATE COMMENT '生日',
    province VARCHAR(50) COMMENT '省份',
    city VARCHAR(50) COMMENT '城市',
    total_points INT DEFAULT 0 COMMENT '总积分',
    available_points INT DEFAULT 0 COMMENT '可用积分',
    level INT DEFAULT 1 COMMENT '用户等级',
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '用户状态',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_phone (phone),
    INDEX idx_nickname (nickname),
    INDEX idx_total_points (total_points),
    INDEX idx_level (level),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';-- ====
=========================================
-- APP分类表
-- =============================================
CREATE TABLE ct_app_categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
    category_code VARCHAR(50) NOT NULL UNIQUE COMMENT '分类代码',
    parent_id INT DEFAULT NULL COMMENT '父分类ID',
    category_icon VARCHAR(500) COMMENT '分类图标URL',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_category_code (category_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='APP分类表';

-- =============================================
-- 文件分类表
-- =============================================
CREATE TABLE ct_file_categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    category_name VARCHAR(50) NOT NULL COMMENT '分类名称',
    category_code VARCHAR(20) NOT NULL UNIQUE COMMENT '分类代码',
    parent_id INT DEFAULT NULL COMMENT '父分类ID',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    max_file_size BIGINT DEFAULT 10485760 COMMENT '最大文件大小(字节)，默认10MB',
    allowed_extensions VARCHAR(200) DEFAULT 'jpg,jpeg,png,gif,webp' COMMENT '允许的文件扩展名',
    description VARCHAR(200) DEFAULT NULL COMMENT '分类描述',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用(0:禁用 1:启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_category_code (category_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件分类表';

-- =============================================
-- 用户登录记录表
-- =============================================
CREATE TABLE ct_user_logins (
    login_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '登录记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    login_type ENUM('password', 'sms', 'wechat', 'qq') DEFAULT 'password' COMMENT '登录方式',
    login_ip VARCHAR(45) COMMENT '登录IP地址',
    login_device VARCHAR(200) COMMENT '登录设备信息',
    user_agent VARCHAR(500) COMMENT '用户代理',
    login_status ENUM('success', 'failed') DEFAULT 'success' COMMENT '登录状态',
    failure_reason VARCHAR(200) COMMENT '失败原因',
    session_id VARCHAR(100) COMMENT '会话ID',
    login_duration INT COMMENT '登录时长(秒)',
    logout_at TIMESTAMP NULL COMMENT '登出时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_login_ip (login_ip),
    INDEX idx_login_status (login_status),
    INDEX idx_created_at (created_at),
    INDEX idx_session_id (session_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户登录记录表';-- =
============================================
-- 用户关系表
-- =============================================
CREATE TABLE ct_user_relations (
    relation_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关系ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    related_user_id BIGINT NOT NULL COMMENT '关联用户ID',
    relation_type ENUM('follow', 'friend', 'block', 'invite') NOT NULL COMMENT '关系类型',
    status ENUM('pending', 'accepted', 'rejected') DEFAULT 'accepted' COMMENT '关系状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_related_user_id (related_user_id),
    INDEX idx_relation_type (relation_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    
    UNIQUE KEY uk_user_relation (user_id, related_user_id, relation_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户关系表';

-- =============================================
-- APP信息表
-- =============================================
CREATE TABLE ct_apps (
    app_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '应用ID',
    category_id INT NOT NULL COMMENT '分类ID',
    app_name VARCHAR(200) NOT NULL COMMENT '应用名称',
    app_package VARCHAR(200) COMMENT '应用包名',
    app_version VARCHAR(50) COMMENT '应用版本',
    developer VARCHAR(200) COMMENT '开发商',
    app_size BIGINT COMMENT '应用大小(字节)',
    download_url VARCHAR(500) COMMENT '下载链接',
    logo_url VARCHAR(500) COMMENT 'Logo图片URL',
    description TEXT COMMENT '应用描述',
    features JSON COMMENT '应用特性(JSON格式)',
    screenshots JSON COMMENT '应用截图(JSON格式)',
    rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '平均评分',
    rating_count INT DEFAULT 0 COMMENT '评分人数',
    download_count BIGINT DEFAULT 0 COMMENT '下载次数',
    view_count BIGINT DEFAULT 0 COMMENT '查看次数',
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending' COMMENT '应用状态',
    is_featured TINYINT(1) DEFAULT 0 COMMENT '是否推荐',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_category_id (category_id),
    INDEX idx_app_name (app_name),
    INDEX idx_app_package (app_package),
    INDEX idx_rating (rating),
    INDEX idx_download_count (download_count),
    INDEX idx_status (status),
    INDEX idx_is_featured (is_featured),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='APP信息表';

-- =============================================
-- 文件信息表
-- =============================================
CREATE TABLE ct_files (
    file_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文件ID',
    category_id INT NOT NULL COMMENT '文件分类ID',
    uploader_id BIGINT NOT NULL COMMENT '上传用户ID',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    stored_name VARCHAR(255) NOT NULL COMMENT '存储文件名(UUID)',
    file_path VARCHAR(500) NOT NULL COMMENT '文件存储路径',
    file_url VARCHAR(500) NOT NULL COMMENT '文件访问URL',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(100) NOT NULL COMMENT '文件MIME类型',
    file_extension VARCHAR(10) NOT NULL COMMENT '文件扩展名',
    file_hash VARCHAR(64) NOT NULL COMMENT '文件MD5哈希值',
    width INT DEFAULT NULL COMMENT '图片宽度(像素)',
    height INT DEFAULT NULL COMMENT '图片高度(像素)',
    duration INT DEFAULT NULL COMMENT '视频/音频时长(秒)',
    upload_ip VARCHAR(45) DEFAULT NULL COMMENT '上传IP地址',
    upload_device VARCHAR(100) DEFAULT NULL COMMENT '上传设备信息',
    storage_type ENUM('local', 'oss', 'cos', 'qiniu') DEFAULT 'local' COMMENT '存储类型',
    is_public TINYINT(1) DEFAULT 1 COMMENT '是否公开访问(0:私有 1:公开)',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否已删除(0:正常 1:已删除)',
    deleted_at TIMESTAMP NULL DEFAULT NULL COMMENT '删除时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_category_id (category_id),
    INDEX idx_uploader_id (uploader_id),
    INDEX idx_file_hash (file_hash),
    INDEX idx_file_type (file_type),
    INDEX idx_file_extension (file_extension),
    INDEX idx_storage_type (storage_type),
    INDEX idx_is_public (is_public),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_created_at (created_at),
    INDEX idx_file_size (file_size),
    INDEX idx_uploader_category (uploader_id, category_id),
    INDEX idx_deleted_created (is_deleted, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件信息表';-- =
============================================
-- 积分规则表
-- =============================================
CREATE TABLE ct_point_rules (
    rule_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '规则ID',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    rule_code VARCHAR(50) NOT NULL UNIQUE COMMENT '规则代码',
    action_type VARCHAR(50) NOT NULL COMMENT '动作类型',
    point_value INT NOT NULL COMMENT '积分值',
    daily_limit INT DEFAULT 0 COMMENT '每日限制次数(0表示无限制)',
    total_limit INT DEFAULT 0 COMMENT '总限制次数(0表示无限制)',
    valid_days INT DEFAULT 0 COMMENT '有效天数(0表示永久有效)',
    conditions JSON COMMENT '触发条件(JSON格式)',
    description TEXT COMMENT '规则描述',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_rule_code (rule_code),
    INDEX idx_action_type (action_type),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分规则表';

-- =============================================
-- 奖励配置表
-- =============================================
CREATE TABLE ct_reward_configs (
    config_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    config_type ENUM('point_exchange', 'level_reward', 'activity_reward') NOT NULL COMMENT '配置类型',
    reward_type ENUM('points', 'badge', 'privilege', 'gift') NOT NULL COMMENT '奖励类型',
    reward_value JSON NOT NULL COMMENT '奖励内容(JSON格式)',
    required_points INT DEFAULT 0 COMMENT '所需积分',
    required_level INT DEFAULT 0 COMMENT '所需等级',
    stock_quantity INT DEFAULT -1 COMMENT '库存数量(-1表示无限)',
    used_quantity INT DEFAULT 0 COMMENT '已使用数量',
    valid_start_date DATE COMMENT '有效开始日期',
    valid_end_date DATE COMMENT '有效结束日期',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_type (config_type),
    INDEX idx_reward_type (reward_type),
    INDEX idx_required_points (required_points),
    INDEX idx_required_level (required_level),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order),
    INDEX idx_valid_dates (valid_start_date, valid_end_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='奖励配置表';

-- =============================================
-- 系统配置表
-- =============================================
CREATE TABLE ct_system_configs (
    config_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键名',
    config_name VARCHAR(200) NOT NULL COMMENT '配置名称',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json', 'text') DEFAULT 'string' COMMENT '配置类型',
    config_group VARCHAR(50) DEFAULT 'system' COMMENT '配置分组',
    config_description TEXT COMMENT '配置描述',
    default_value TEXT COMMENT '默认值',
    validation_rule VARCHAR(500) COMMENT '验证规则',
    is_encrypted TINYINT(1) DEFAULT 0 COMMENT '是否加密存储(0:否 1:是)',
    is_public TINYINT(1) DEFAULT 0 COMMENT '是否公开访问(0:否 1:是)',
    is_editable TINYINT(1) DEFAULT 1 COMMENT '是否可编辑(0:否 1:是)',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用(0:禁用 1:启用)',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    version INT DEFAULT 1 COMMENT '配置版本号',
    created_by BIGINT DEFAULT NULL COMMENT '创建人ID',
    updated_by BIGINT DEFAULT NULL COMMENT '最后更新人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_config_key (config_key),
    INDEX idx_config_group (config_group),
    INDEX idx_config_type (config_type),
    INDEX idx_is_active (is_active),
    INDEX idx_is_public (is_public),
    INDEX idx_sort_order (sort_order),
    INDEX idx_created_at (created_at),
    INDEX idx_updated_at (updated_at),
    INDEX idx_group_active (config_group, is_active),
    INDEX idx_public_active (is_public, is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';-- 
=============================================
-- 审核规则表
-- =============================================
CREATE TABLE ct_audit_rules (
    rule_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '规则ID',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    rule_code VARCHAR(50) NOT NULL UNIQUE COMMENT '规则代码',
    content_type VARCHAR(50) NOT NULL COMMENT '内容类型(evaluation_report,water_clue,user_profile等)',
    rule_description TEXT COMMENT '规则描述',
    auto_audit_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用自动审核(0:否 1:是)',
    auto_audit_keywords TEXT COMMENT '自动审核关键词(JSON格式)',
    manual_audit_required TINYINT(1) DEFAULT 1 COMMENT '是否需要人工审核(0:否 1:是)',
    audit_timeout_hours INT DEFAULT 72 COMMENT '审核超时时间(小时)',
    pass_score_threshold INT DEFAULT 80 COMMENT '自动通过分数阈值',
    reject_score_threshold INT DEFAULT 20 COMMENT '自动拒绝分数阈值',
    reward_points INT DEFAULT 0 COMMENT '审核通过奖励积分',
    penalty_points INT DEFAULT 0 COMMENT '审核不通过扣除积分',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用(0:禁用 1:启用)',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    created_by BIGINT DEFAULT NULL COMMENT '创建人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_rule_code (rule_code),
    INDEX idx_content_type (content_type),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审核规则表';

-- =============================================
-- 评测报告表
-- =============================================
CREATE TABLE ct_evaluation_reports (
    report_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '报告ID',
    app_id BIGINT NOT NULL COMMENT '应用ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    report_title VARCHAR(200) NOT NULL COMMENT '报告标题',
    report_content TEXT NOT NULL COMMENT '报告内容',
    task_description TEXT COMMENT '任务描述',
    completion_time INT COMMENT '完成时长(分钟)',
    difficulty_level ENUM('easy', 'medium', 'hard') DEFAULT 'medium' COMMENT '难度等级',
    rating TINYINT CHECK (rating >= 1 AND rating <= 5) COMMENT '评分(1-5)',
    pros TEXT COMMENT '优点',
    cons TEXT COMMENT '缺点',
    suggestions TEXT COMMENT '改进建议',
    screenshots JSON COMMENT '截图列表(JSON格式)',
    reward_points INT DEFAULT 0 COMMENT '奖励积分',
    status ENUM('draft', 'submitted', 'approved', 'rejected') DEFAULT 'draft' COMMENT '状态',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    like_count INT DEFAULT 0 COMMENT '点赞次数',
    is_featured TINYINT(1) DEFAULT 0 COMMENT '是否推荐',
    submitted_at TIMESTAMP NULL COMMENT '提交时间',
    approved_at TIMESTAMP NULL COMMENT '审核通过时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_app_id (app_id),
    INDEX idx_user_id (user_id),
    INDEX idx_rating (rating),
    INDEX idx_status (status),
    INDEX idx_is_featured (is_featured),
    INDEX idx_submitted_at (submitted_at),
    INDEX idx_created_at (created_at),
    INDEX idx_app_user (app_id, user_id),
    INDEX idx_status_submitted (status, submitted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评测报告表';-- =========
====================================
-- 评测数据详情表
-- =============================================
CREATE TABLE ct_evaluation_details (
    detail_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '详情ID',
    report_id BIGINT NOT NULL COMMENT '报告ID',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    metric_value VARCHAR(500) NOT NULL COMMENT '指标值',
    metric_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text' COMMENT '指标类型',
    metric_unit VARCHAR(20) COMMENT '指标单位',
    metric_description TEXT COMMENT '指标描述',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_report_id (report_id),
    INDEX idx_metric_name (metric_name),
    INDEX idx_sort_order (sort_order),
    INDEX idx_report_metric (report_id, metric_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评测数据详情表';

-- =============================================
-- 放水线索表
-- =============================================
CREATE TABLE ct_water_clues (
    clue_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '线索ID',
    app_id BIGINT NOT NULL COMMENT '应用ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    clue_title VARCHAR(200) NOT NULL COMMENT '线索标题',
    clue_content TEXT NOT NULL COMMENT '线索内容',
    clue_type ENUM('bug', 'loophole', 'promotion', 'other') DEFAULT 'other' COMMENT '线索类型',
    difficulty_level ENUM('easy', 'medium', 'hard') DEFAULT 'medium' COMMENT '难度等级',
    expected_reward DECIMAL(10,2) COMMENT '预期收益',
    actual_reward DECIMAL(10,2) COMMENT '实际收益',
    success_rate DECIMAL(5,2) COMMENT '成功率(%)',
    risk_level ENUM('low', 'medium', 'high') DEFAULT 'medium' COMMENT '风险等级',
    steps TEXT COMMENT '操作步骤',
    screenshots JSON COMMENT '截图列表(JSON格式)',
    tags VARCHAR(500) COMMENT '标签',
    reward_points INT DEFAULT 0 COMMENT '奖励积分',
    status ENUM('draft', 'submitted', 'approved', 'rejected', 'expired') DEFAULT 'draft' COMMENT '状态',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    like_count INT DEFAULT 0 COMMENT '点赞次数',
    try_count INT DEFAULT 0 COMMENT '尝试次数',
    success_count INT DEFAULT 0 COMMENT '成功次数',
    is_featured TINYINT(1) DEFAULT 0 COMMENT '是否推荐',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    submitted_at TIMESTAMP NULL COMMENT '提交时间',
    approved_at TIMESTAMP NULL COMMENT '审核通过时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_app_id (app_id),
    INDEX idx_user_id (user_id),
    INDEX idx_clue_type (clue_type),
    INDEX idx_difficulty_level (difficulty_level),
    INDEX idx_risk_level (risk_level),
    INDEX idx_status (status),
    INDEX idx_is_featured (is_featured),
    INDEX idx_expires_at (expires_at),
    INDEX idx_submitted_at (submitted_at),
    INDEX idx_created_at (created_at),
    INDEX idx_app_user (app_id, user_id),
    INDEX idx_status_submitted (status, submitted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='放水线索表';

-- =============================================
-- 线索反馈表
-- =============================================
CREATE TABLE ct_clue_feedbacks (
    feedback_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '反馈ID',
    clue_id BIGINT NOT NULL COMMENT '线索ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    feedback_type ENUM('success', 'failure', 'question', 'suggestion') NOT NULL COMMENT '反馈类型',
    feedback_content TEXT NOT NULL COMMENT '反馈内容',
    actual_reward DECIMAL(10,2) COMMENT '实际收益',
    time_spent INT COMMENT '耗时(分钟)',
    difficulty_rating TINYINT CHECK (difficulty_rating >= 1 AND difficulty_rating <= 5) COMMENT '难度评分',
    success_rating TINYINT CHECK (success_rating >= 1 AND success_rating <= 5) COMMENT '成功率评分',
    screenshots JSON COMMENT '截图证明',
    is_verified TINYINT(1) DEFAULT 0 COMMENT '是否已验证',
    verified_by BIGINT COMMENT '验证人ID',
    verified_at TIMESTAMP NULL COMMENT '验证时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_clue_id (clue_id),
    INDEX idx_user_id (user_id),
    INDEX idx_feedback_type (feedback_type),
    INDEX idx_is_verified (is_verified),
    INDEX idx_created_at (created_at),
    INDEX idx_clue_user (clue_id, user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索反馈表';-- ===
==========================================
-- 线索统计表
-- =============================================
CREATE TABLE ct_clue_statistics (
    stat_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计ID',
    clue_id BIGINT NOT NULL COMMENT '线索ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    try_count INT DEFAULT 0 COMMENT '尝试次数',
    success_count INT DEFAULT 0 COMMENT '成功次数',
    failure_count INT DEFAULT 0 COMMENT '失败次数',
    total_reward DECIMAL(12,2) DEFAULT 0.00 COMMENT '总收益',
    avg_reward DECIMAL(10,2) DEFAULT 0.00 COMMENT '平均收益',
    success_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '成功率',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_clue_id (clue_id),
    INDEX idx_stat_date (stat_date),
    INDEX idx_success_rate (success_rate),
    INDEX idx_clue_date (clue_id, stat_date),
    
    UNIQUE KEY uk_clue_date (clue_id, stat_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索统计表';

-- =============================================
-- 积分记录表
-- =============================================
CREATE TABLE ct_point_records (
    record_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    rule_id INT COMMENT '规则ID',
    point_change INT NOT NULL COMMENT '积分变化',
    point_balance INT NOT NULL COMMENT '积分余额',
    change_type ENUM('earn', 'spend', 'expire', 'adjust') NOT NULL COMMENT '变化类型',
    source_type VARCHAR(50) NOT NULL COMMENT '来源类型',
    source_id BIGINT COMMENT '来源ID',
    description VARCHAR(500) COMMENT '描述',
    reference_data JSON COMMENT '关联数据',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_rule_id (rule_id),
    INDEX idx_change_type (change_type),
    INDEX idx_source_type (source_type),
    INDEX idx_source_id (source_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at),
    INDEX idx_user_created (user_id, created_at),
    INDEX idx_source_type_id (source_type, source_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分记录表';

-- =============================================
-- 文件关联表
-- =============================================
CREATE TABLE ct_file_relations (
    relation_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    file_id BIGINT NOT NULL COMMENT '文件ID',
    business_type VARCHAR(50) NOT NULL COMMENT '业务类型(app_logo,evaluation_screenshot,clue_screenshot等)',
    business_id BIGINT NOT NULL COMMENT '业务对象ID',
    relation_type VARCHAR(20) DEFAULT 'attachment' COMMENT '关联类型(attachment:附件,cover:封面,thumbnail:缩略图)',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_primary TINYINT(1) DEFAULT 0 COMMENT '是否主要文件(0:否 1:是)',
    description VARCHAR(200) DEFAULT NULL COMMENT '关联描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_file_id (file_id),
    INDEX idx_business_type (business_type),
    INDEX idx_business_id (business_id),
    INDEX idx_relation_type (relation_type),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_primary (is_primary),
    INDEX idx_business_type_id (business_type, business_id),
    INDEX idx_file_business (file_id, business_type, business_id),
    INDEX idx_business_primary (business_type, business_id, is_primary),
    
    UNIQUE KEY uk_file_business_relation (file_id, business_type, business_id, relation_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件关联表';-- ========
=====================================
-- 内容审核表
-- =============================================
CREATE TABLE ct_content_audits (
    audit_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '审核ID',
    rule_id INT NOT NULL COMMENT '审核规则ID',
    content_type VARCHAR(50) NOT NULL COMMENT '内容类型',
    content_id BIGINT NOT NULL COMMENT '内容对象ID',
    submitter_id BIGINT NOT NULL COMMENT '提交者用户ID',
    audit_status ENUM('pending', 'reviewing', 'passed', 'rejected', 'timeout') DEFAULT 'pending' COMMENT '审核状态',
    auto_audit_score INT DEFAULT NULL COMMENT '自动审核分数',
    auto_audit_result ENUM('pass', 'reject', 'manual') DEFAULT NULL COMMENT '自动审核结果',
    auto_audit_reason TEXT COMMENT '自动审核原因',
    manual_audit_required TINYINT(1) DEFAULT 1 COMMENT '是否需要人工审核',
    auditor_id BIGINT DEFAULT NULL COMMENT '审核员ID',
    audit_result ENUM('pass', 'reject') DEFAULT NULL COMMENT '最终审核结果',
    audit_reason TEXT COMMENT '审核原因/备注',
    audit_score INT DEFAULT NULL COMMENT '人工审核分数',
    reject_category VARCHAR(50) DEFAULT NULL COMMENT '拒绝分类(spam,inappropriate,fake等)',
    content_snapshot JSON COMMENT '内容快照(审核时的内容状态)',
    priority_level ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal' COMMENT '优先级',
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '提交审核时间',
    audit_started_at TIMESTAMP NULL DEFAULT NULL COMMENT '开始审核时间',
    audit_completed_at TIMESTAMP NULL DEFAULT NULL COMMENT '审核完成时间',
    timeout_at TIMESTAMP NULL DEFAULT NULL COMMENT '审核超时时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_rule_id (rule_id),
    INDEX idx_content_type (content_type),
    INDEX idx_content_id (content_id),
    INDEX idx_submitter_id (submitter_id),
    INDEX idx_auditor_id (auditor_id),
    INDEX idx_audit_status (audit_status),
    INDEX idx_audit_result (audit_result),
    INDEX idx_priority_level (priority_level),
    INDEX idx_submitted_at (submitted_at),
    INDEX idx_audit_completed_at (audit_completed_at),
    INDEX idx_timeout_at (timeout_at),
    INDEX idx_content_type_id (content_type, content_id),
    INDEX idx_status_priority (audit_status, priority_level),
    INDEX idx_auditor_status (auditor_id, audit_status),
    INDEX idx_submitter_status (submitter_id, audit_status),
    
    UNIQUE KEY uk_content_audit (content_type, content_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容审核表';

-- =============================================
-- 审核日志表
-- =============================================
CREATE TABLE ct_audit_logs (
    log_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    audit_id BIGINT NOT NULL COMMENT '审核ID',
    operator_id BIGINT DEFAULT NULL COMMENT '操作人ID',
    operator_type ENUM('system', 'admin', 'auto') DEFAULT 'system' COMMENT '操作人类型',
    action_type VARCHAR(50) NOT NULL COMMENT '操作类型(submit,assign,review,approve,reject,timeout等)',
    old_status VARCHAR(20) DEFAULT NULL COMMENT '原状态',
    new_status VARCHAR(20) NOT NULL COMMENT '新状态',
    action_reason TEXT COMMENT '操作原因',
    action_data JSON COMMENT '操作相关数据',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT '操作IP地址',
    user_agent VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
    processing_time INT DEFAULT NULL COMMENT '处理耗时(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_audit_id (audit_id),
    INDEX idx_operator_id (operator_id),
    INDEX idx_operator_type (operator_type),
    INDEX idx_action_type (action_type),
    INDEX idx_old_status (old_status),
    INDEX idx_new_status (new_status),
    INDEX idx_created_at (created_at),
    INDEX idx_audit_action (audit_id, action_type),
    INDEX idx_operator_action (operator_id, action_type),
    INDEX idx_status_change (old_status, new_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审核日志表';-- 
=============================================
-- 操作日志表
-- =============================================
CREATE TABLE ct_operation_logs (
    log_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    operator_id BIGINT DEFAULT NULL COMMENT '操作人ID',
    operator_type ENUM('user', 'admin', 'system', 'api') DEFAULT 'user' COMMENT '操作人类型',
    operator_name VARCHAR(100) DEFAULT NULL COMMENT '操作人名称',
    module VARCHAR(50) NOT NULL COMMENT '操作模块',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) DEFAULT NULL COMMENT '资源类型',
    resource_id BIGINT DEFAULT NULL COMMENT '资源ID',
    operation_desc TEXT COMMENT '操作描述',
    request_method VARCHAR(10) DEFAULT NULL COMMENT '请求方法(GET,POST,PUT,DELETE)',
    request_url VARCHAR(500) DEFAULT NULL COMMENT '请求URL',
    request_params JSON COMMENT '请求参数',
    response_data JSON COMMENT '响应数据',
    operation_result ENUM('success', 'failure', 'partial') DEFAULT 'success' COMMENT '操作结果',
    error_message TEXT COMMENT '错误信息',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT '操作IP地址',
    user_agent VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
    session_id VARCHAR(100) DEFAULT NULL COMMENT '会话ID',
    trace_id VARCHAR(100) DEFAULT NULL COMMENT '链路追踪ID',
    processing_time INT DEFAULT NULL COMMENT '处理耗时(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_operator_id (operator_id),
    INDEX idx_operator_type (operator_type),
    INDEX idx_module (module),
    INDEX idx_action (action),
    INDEX idx_resource_type (resource_type),
    INDEX idx_resource_id (resource_id),
    INDEX idx_operation_result (operation_result),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_at (created_at),
    INDEX idx_session_id (session_id),
    INDEX idx_trace_id (trace_id),
    INDEX idx_operator_module (operator_id, module),
    INDEX idx_module_action (module, action),
    INDEX idx_resource_type_id (resource_type, resource_id),
    INDEX idx_result_time (operation_result, created_at),
    INDEX idx_operator_time (operator_id, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- =============================================
-- 统计数据表
-- =============================================
CREATE TABLE ct_statistics (
    stat_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计ID',
    stat_key VARCHAR(100) NOT NULL COMMENT '统计键名',
    stat_name VARCHAR(200) NOT NULL COMMENT '统计名称',
    stat_type ENUM('counter', 'gauge', 'histogram', 'summary') DEFAULT 'counter' COMMENT '统计类型',
    stat_category VARCHAR(50) NOT NULL COMMENT '统计分类',
    stat_dimension VARCHAR(100) DEFAULT NULL COMMENT '统计维度',
    stat_value DECIMAL(20,4) NOT NULL DEFAULT 0 COMMENT '统计值',
    stat_count BIGINT DEFAULT 0 COMMENT '统计次数',
    stat_sum DECIMAL(20,4) DEFAULT 0 COMMENT '统计总和',
    stat_avg DECIMAL(20,4) DEFAULT 0 COMMENT '统计平均值',
    stat_min DECIMAL(20,4) DEFAULT 0 COMMENT '统计最小值',
    stat_max DECIMAL(20,4) DEFAULT 0 COMMENT '统计最大值',
    stat_data JSON COMMENT '扩展统计数据',
    time_period ENUM('realtime', 'hourly', 'daily', 'weekly', 'monthly', 'yearly') DEFAULT 'daily' COMMENT '时间周期',
    stat_date DATE NOT NULL COMMENT '统计日期',
    stat_hour TINYINT DEFAULT NULL COMMENT '统计小时(0-23)',
    stat_week TINYINT DEFAULT NULL COMMENT '统计周(1-53)',
    stat_month TINYINT DEFAULT NULL COMMENT '统计月(1-12)',
    stat_year SMALLINT DEFAULT NULL COMMENT '统计年',
    is_calculated TINYINT(1) DEFAULT 0 COMMENT '是否已计算(0:否 1:是)',
    calculation_time TIMESTAMP NULL DEFAULT NULL COMMENT '计算时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_stat_key (stat_key),
    INDEX idx_stat_category (stat_category),
    INDEX idx_stat_type (stat_type),
    INDEX idx_time_period (time_period),
    INDEX idx_stat_date (stat_date),
    INDEX idx_stat_hour (stat_hour),
    INDEX idx_stat_week (stat_week),
    INDEX idx_stat_month (stat_month),
    INDEX idx_stat_year (stat_year),
    INDEX idx_is_calculated (is_calculated),
    INDEX idx_created_at (created_at),
    INDEX idx_key_date (stat_key, stat_date),
    INDEX idx_category_date (stat_category, stat_date),
    INDEX idx_key_period_date (stat_key, time_period, stat_date),
    INDEX idx_category_period_date (stat_category, time_period, stat_date),
    INDEX idx_date_period (stat_date, time_period),
    
    UNIQUE KEY uk_stat_unique (stat_key, stat_category, stat_dimension, time_period, stat_date, stat_hour)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='统计数据表';-- ==
===========================================
-- 初始化数据
-- =============================================

-- 插入APP分类基础数据
INSERT INTO ct_app_categories (category_name, category_code, sort_order) VALUES
('社交通讯', 'social', 1),
('金融理财', 'finance', 2),
('生活服务', 'lifestyle', 3),
('购物消费', 'shopping', 4),
('游戏娱乐', 'game', 5),
('工具效率', 'tools', 6),
('其他', 'other', 99);

-- 插入文件分类基础数据
INSERT INTO ct_file_categories (category_name, category_code, parent_id, sort_order, max_file_size, allowed_extensions, description) VALUES
('APP图标', 'app_logo', NULL, 1, 2097152, 'jpg,jpeg,png,webp', 'APP应用图标文件'),
('评测截图', 'evaluation_screenshot', NULL, 2, 5242880, 'jpg,jpeg,png,webp', '评测报告相关截图'),
('线索截图', 'clue_screenshot', NULL, 3, 5242880, 'jpg,jpeg,png,webp', '放水线索相关截图'),
('用户头像', 'user_avatar', NULL, 4, 1048576, 'jpg,jpeg,png,webp', '用户头像图片'),
('系统图片', 'system_image', NULL, 5, 10485760, 'jpg,jpeg,png,gif,webp', '系统使用的图片资源'),
('文档附件', 'document', NULL, 6, 52428800, 'pdf,doc,docx,xls,xlsx,txt', '文档类附件'),
('其他文件', 'other', NULL, 99, 10485760, 'jpg,jpeg,png,gif,webp,pdf,txt', '其他类型文件');

-- 插入积分规则基础数据
INSERT INTO ct_point_rules (rule_name, rule_code, action_type, point_value, daily_limit, description) VALUES
('用户注册奖励', 'user_register', 'register', 50, 1, '新用户注册奖励积分'),
('每日签到奖励', 'daily_signin', 'signin', 2, 1, '每日签到获得积分'),
('评测报告奖励', 'evaluation_reward', 'evaluation', 10, 5, '提交评测报告获得积分'),
('线索提交奖励', 'clue_reward', 'clue', 5, 10, '提交放水线索获得积分'),
('邀请好友奖励', 'invite_reward', 'invite', 20, 10, '成功邀请好友获得积分');

-- 插入奖励配置基础数据
INSERT INTO ct_reward_configs (config_name, config_type, reward_type, reward_value, required_points, stock_quantity, sort_order) VALUES
('10元话费充值', 'point_exchange', 'gift', '{"type": "phone_credit", "amount": 10}', 1000, 100, 1),
('20元话费充值', 'point_exchange', 'gift', '{"type": "phone_credit", "amount": 20}', 2000, 50, 2),
('50元话费充值', 'point_exchange', 'gift', '{"type": "phone_credit", "amount": 50}', 5000, 20, 3),
('VIP会员特权', 'point_exchange', 'privilege', '{"type": "vip", "duration": 30}', 3000, 10, 4);

-- 插入系统配置基础数据
INSERT INTO ct_system_configs (config_key, config_name, config_value, config_type, config_group, config_description, default_value, is_public, sort_order) VALUES
-- 积分系统配置
('point.evaluation.reward', '评测报告奖励积分', '10', 'number', 'point', '用户提交评测报告获得的积分奖励', '10', 0, 1),
('point.clue.reward', '线索提交奖励积分', '5', 'number', 'point', '用户提交放水线索获得的积分奖励', '5', 0, 2),
('point.daily.signin', '每日签到积分', '2', 'number', 'point', '用户每日签到获得的积分', '2', 0, 3),
('point.invite.reward', '邀请奖励积分', '20', 'number', 'point', '成功邀请新用户获得的积分奖励', '20', 0, 4),

-- APP分类配置
('app.categories', 'APP分类配置', '["社交通讯","金融理财","生活服务","购物消费","游戏娱乐","工具效率","其他"]', 'json', 'app', 'APP应用的分类列表', '[]', 1, 10),
('app.logo.max_size', 'APP图标最大尺寸', '2097152', 'number', 'app', 'APP图标文件的最大字节数(2MB)', '2097152', 0, 11),
('app.logo.allowed_formats', 'APP图标允许格式', 'jpg,jpeg,png,webp', 'string', 'app', 'APP图标允许的文件格式', 'jpg,jpeg,png,webp', 0, 12),

-- 审核系统配置
('audit.auto_enabled', '启用自动审核', 'true', 'boolean', 'audit', '是否启用自动审核功能', 'true', 0, 20),
('audit.timeout_hours', '默认审核超时时间', '48', 'number', 'audit', '审核任务的默认超时时间(小时)', '48', 0, 21),
('audit.keywords.sensitive', '敏感词库', '["违法","色情","暴力","赌博","毒品"]', 'json', 'audit', '自动审核的敏感词库', '[]', 0, 22),
('audit.pass_threshold', '自动通过阈值', '80', 'number', 'audit', '自动审核通过的分数阈值', '80', 0, 23),
('audit.reject_threshold', '自动拒绝阈值', '20', 'number', 'audit', '自动审核拒绝的分数阈值', '20', 0, 24),

-- 文件系统配置
('file.upload.max_size', '文件上传最大尺寸', '10485760', 'number', 'file', '单个文件上传的最大字节数(10MB)', '10485760', 0, 30),
('file.storage.type', '文件存储类型', 'local', 'string', 'file', '文件存储方式(local,oss,cos,qiniu)', 'local', 0, 31),
('file.cdn.domain', 'CDN域名', '', 'string', 'file', '文件访问的CDN域名', '', 0, 32),

-- 系统基础配置
('system.name', '系统名称', '次推', 'string', 'system', '系统的显示名称', '次推', 1, 100),
('system.version', '系统版本', '1.0.0', 'string', 'system', '当前系统版本号', '1.0.0', 1, 101),
('system.maintenance', '维护模式', 'false', 'boolean', 'system', '是否开启系统维护模式', 'false', 0, 102),
('system.register_enabled', '开放注册', 'true', 'boolean', 'system', '是否允许用户注册', 'true', 1, 103),

-- 通知配置
('notification.sms.enabled', '短信通知启用', 'true', 'boolean', 'notification', '是否启用短信通知功能', 'true', 0, 110),
('notification.email.enabled', '邮件通知启用', 'false', 'boolean', 'notification', '是否启用邮件通知功能', 'false', 0, 111);

-- 插入审核规则基础数据
INSERT INTO ct_audit_rules (rule_name, rule_code, content_type, rule_description, auto_audit_enabled, manual_audit_required, audit_timeout_hours, reward_points, penalty_points, sort_order) VALUES
('评测报告审核', 'evaluation_report_audit', 'evaluation_report', '评测报告内容审核规则', 1, 1, 48, 10, 5, 1),
('放水线索审核', 'water_clue_audit', 'water_clue', '放水线索内容审核规则', 1, 1, 24, 5, 3, 2),
('用户资料审核', 'user_profile_audit', 'user_profile', '用户资料信息审核规则', 0, 1, 72, 0, 0, 3),
('用户头像审核', 'user_avatar_audit', 'user_avatar', '用户头像图片审核规则', 1, 0, 12, 0, 0, 4),
('APP图标审核', 'app_logo_audit', 'app_logo', 'APP图标审核规则', 1, 0, 6, 0, 0, 5);

-- =============================================
-- 脚本执行完成
-- =============================================

SELECT '次推应用数据库表结构创建完成！' as message,
       COUNT(*) as total_tables
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name LIKE 'ct_%';
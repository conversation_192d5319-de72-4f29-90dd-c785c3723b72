<?php

declare(strict_types=1);

namespace App\Models\Citui;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Builder;

class EvaluationReport extends BaseModel
{
    protected $table = 'evaluation_reports';

    protected $fillable = [
        'app_id',
        'user_id',
        'report_title',
        'report_content',
        'download_url',
        'pingfen',
        'yunxingmoshi',
        'xinrenfuli',
        'tixianmenkan',
        'dingbaojine',
        'ceshitiaoshu',
        'ceshishouyi',
        'ceshishichang',
        'ceshishebei',
        'cepingren',
        'status',
        'view_count',
        'like_count',
        'is_featured',
        'cepingriqi',
        'shouyi_1',
        'shouyi_2',
        'shouyi_3',
        'shouyi_4',
        'shouyi_5',
        'pic_main',
        'pic_tixian',
        'pic_daozhang',
        'submitted_at',
        'approved_at',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'pingfen' => 'integer',
        'yunxingmoshi' => 'integer',
        'xinrenfuli' => 'decimal:2',
        'tixianmenkan' => 'decimal:2',
        'dingbaojine' => 'decimal:2',
        'ceshitiaoshu' => 'integer',
        'ceshishouyi' => 'decimal:2',
        'ceshishichang' => 'decimal:2',
        'shouyi_1' => 'decimal:2',
        'shouyi_2' => 'decimal:2',
        'shouyi_3' => 'decimal:2',
        'shouyi_4' => 'decimal:2',
        'shouyi_5' => 'decimal:2',
        'status' => 'integer',
        'view_count' => 'integer',
        'like_count' => 'integer',
        'is_featured' => 'boolean',
        'cepingriqi' => 'date',
        'submitted_at' => 'datetime',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 关联到APP表
     */
    public function app()
    {
        return $this->belongsTo(App::class, 'app_id');
    }
}

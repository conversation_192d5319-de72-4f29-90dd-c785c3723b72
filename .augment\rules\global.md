---
type: "always_apply"
---

项目结构说明和开发规范：

**项目根目录定义：**
- 项目根目录：D:\mywork\cituiproject

**前端项目配置：**
- 前端项目目录：D:\mywork\cituiproject\cituiapp
- 技术栈：UNIAPP + UVIEW2 框架
- 网络请求配置文件：cituiapp\config\request.js
- 网络请求参考示例：
  - GET/POST 请求示例：cituiapp\pages\login\login.vue 和 cituiapp\pages\index\index.vue
  - 表单提交示例：cituiapp\pages\submit-report\submit-report.vue

**后端项目配置：**
- 后端项目目录：D:\mywork\cituiproject\cituilaravel
- 技术栈：Laravel 9 框架
- 数据库：MySQL 5.7
- 数据库结构文件：cituilaravel\citui.sql

**后端目录结构：**
- 数据库模型：cituilaravel\app\Models（注意：模型配置表名时无需添加 zc_ 前缀，已全局配置）
- API控制器：cituilaravel\app\Http\Controllers\Api\V1\Wap
- 业务服务层：cituilaravel\app\Service
- 工具类：cituilaravel\app\Helpers
- 通用方法：cituilaravel\app\Utils
- API路由配置：cituilaravel\routes\api.php
- 中间件：cituilaravel\app\Http\Middleware

**开发规范：**
在修改前端或后端项目代码之前，必须：
1. 先查找并阅读相关的参考文件
2. 熟悉现有代码风格和架构模式
3. 确保新代码与现有代码风格保持一致
4. 遵循项目既定的开发规范和最佳实践

此规则说明位于：D:\mywork\cituiproject\.augment\rules\global.md
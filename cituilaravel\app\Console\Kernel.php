<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // 每天00:05执行日志清理
        $schedule->command('system:log_clear')->dailyAt('00:05');
        
        // CitUI文件清理任务
        if (config('citui_file.cleanup.auto_cleanup', true)) {
            // 每天凌晨2点清理临时文件
            $schedule->command('citui:cleanup-files --type=temp --force')
                     ->dailyAt('02:00')
                     ->withoutOverlapping()
                     ->runInBackground();
            
            // 每周日凌晨3点清理软删除文件
            $schedule->command('citui:cleanup-files --type=deleted --force')
                     ->weeklyOn(0, '03:00')
                     ->withoutOverlapping()
                     ->runInBackground();
            
            // 每月1号凌晨4点清理孤立文件
            $schedule->command('citui:cleanup-files --type=orphaned --force')
                     ->monthlyOn(1, '04:00')
                     ->withoutOverlapping()
                     ->runInBackground();
        }
        
        // CitUI文件安全扫描任务
        if (config('citui_file.security.enable_content_scan', true)) {
            // 每天凌晨1点执行安全扫描
            $schedule->command('citui:scan-files --type=security --quarantine')
                     ->dailyAt('01:00')
                     ->withoutOverlapping()
                     ->runInBackground();
            
            // 每周六凌晨5点执行完整性扫描
            $schedule->command('citui:scan-files --type=integrity')
                     ->weeklyOn(6, '05:00')
                     ->withoutOverlapping()
                     ->runInBackground();
        }
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');
        require base_path('routes/console.php');
    }
}

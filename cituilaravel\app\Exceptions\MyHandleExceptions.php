<?php
declare(strict_types=1);
namespace App\Exceptions;
use ErrorException;
use Illuminate\Foundation\Bootstrap\HandleExceptions;

class MyHandleExceptions extends HandleExceptions
{
    /**
     * Convert PHP errors to ErrorException instances.
     *
     * @param int $level
     * @param string $message
     * @param string $file
     * @param int $line
     * @param array $context
     * @return mixed
     *
     * @throws ErrorException
     */
    public function handleError($level, $message, $file = '', $line = 0, $context = [])
    {
        // 忽略 E_USER_DEPRECATED 抛出异常
        if ($level == E_USER_DEPRECATED) return true;
        if (error_reporting() & $level) {
            throw new ErrorException($message, 0, $level, $file, $line);
        }
    }
}

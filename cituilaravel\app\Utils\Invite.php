<?php
declare(strict_types=1);

namespace App\Utils;
use Hashids\Hashids;
class Invite
{
    public function encode(int $user_id = 0): string
    {
        if($user_id<=0) return '';
        $hashids = new Hashids('',10);
        return $hashids->encode($user_id);
    }

    public function decode(string $code): int|array
    {
        if(empty($code)) return 0;
        $hashids = new Hashids('',10);
        $result = $hashids->decode($code);
        if(!is_array($result)){
            return 0;
        }
        if(count($result)==0){
            return 0;
        }
        if(count($result)==1){
            return $result[0];
        }
        return $result;
    }

    public function encodeHex(int $user_id = 0): string
    {
        if($user_id<=0) return '';
        $hashids = new Hashids('',15);
        return $hashids->encodeHex((string)$user_id);
    }

    public function decodeHex(string $code): int
    {
        if(empty($code)) return 0;
        $hashids = new Hashids('',15);
        $result = $hashids->decodeHex($code);
        if(empty($result)){
            return 0;
        }
        if(!is_numeric($result)){
            return 0;
        }
        return (int)$result;
    }

}

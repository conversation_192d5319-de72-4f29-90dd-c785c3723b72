import Vue from 'vue'
import Vuex from 'vuex'
import { getToken, setToken, removeToken } from '../utils/storage.js'

Vue.use(Vuex)

const store = new Vuex.Store({
	state: {
		// 用户信息
		userInfo: {},
		// 登录状态
		isLoggedIn: false,
		// token
		token: getToken()
	},
	
	mutations: {
		// 设置用户信息
		SET_USER_INFO(state, userInfo) {
			state.userInfo = userInfo
			state.isLoggedIn = true
			// 同步到本地存储
			uni.setStorageSync('userInfo', userInfo)
		},
		
		// 设置token
		SET_TOKEN(state, token) {
			state.token = token
			setToken(token)
		},
		
		// 清除用户信息
		CLEAR_USER_INFO(state) {
			state.userInfo = {}
			state.isLoggedIn = false
			state.token = ''
			// 清除本地存储
			uni.removeStorageSync('userInfo')
			removeToken()
		},
		
		// 初始化用户状态（从本地存储恢复）
		INIT_USER_STATE(state) {
			const token = getToken()
			const userInfo = uni.getStorageSync('userInfo')
			
			if (token && userInfo) {
				state.token = token
				state.userInfo = userInfo
				state.isLoggedIn = true
			}
		}
	},
	
	actions: {
		// 登录
		login({ commit }, { userInfo, token }) {
			commit('SET_USER_INFO', userInfo)
			commit('SET_TOKEN', token)
		},
		
		// 登出
		logout({ commit }) {
			commit('CLEAR_USER_INFO')
		},
		
		// 初始化用户状态
		initUserState({ commit }) {
			commit('INIT_USER_STATE')
		}
	},
	
	getters: {
		// 获取用户信息
		getUserInfo: state => state.userInfo,
		
		// 获取登录状态
		getLoginStatus: state => state.isLoggedIn,
		
		// 获取token
		getToken: state => state.token
	}
})

export default store
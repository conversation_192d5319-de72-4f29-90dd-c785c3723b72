<?php
declare(strict_types=1);

namespace App\Http\Requests\Citui;

class AppCategoriesRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'parent_id' => 'nullable|integer|exists:ct_app_categories,category_id',
            'with_tree' => 'nullable|boolean'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return array_merge(parent::attributes(), [
            'parent_id' => '父分类ID',
            'with_tree' => '返回树形结构'
        ]);
    }
}
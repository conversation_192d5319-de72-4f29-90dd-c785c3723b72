<?php
declare(strict_types=1);

namespace App\Service\Clue;

use App\Service\BaseService;
use App\Models\Citui\App;
use App\Models\Citui\WaterClues;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ClueService extends BaseService
{

    /**
     * 提交线索
     * @param array $data
     * @return array
     * @throws \Exception
     */
    public function submitClue(array $data): array
    {
        try {
            DB::beginTransaction();

            // 获取当前用户ID（假设已通过中间件验证）
            $userId = Auth::id() ?? 0;

            // 验证APP是否存在
            $app = App::find($data['app_id']);
            if (!$app) {
                throw new \Exception('选择的APP不存在');
            }

            // 处理单包大小映射
            $packageMoneyMap = [
                '0.1-0.29' => 1,
                '0.3-0.49' => 2,
                '0.5-0.99' => 3,
                '1以上' => 4
            ];

            $packageMoney = $packageMoneyMap[$data['package_amount']] ?? 1;

            // 生成线索标题（后端自动拼接）
            $clueTitle = $app->app_name . ' - 放水线索 - ' . date('Y-m-d H:i');

            // 准备插入数据
            $clueData = [
                'app_id' => $data['app_id'],
                'user_id' => $userId,
                'clue_title' => $clueTitle,
                'clue_content' => $data['clue_description'],
                'clue_time' => $data['release_time'] ? date('Y-m-d H:i:s', $data['release_time'] / 1000) : now(),
                'clue_money' => $data['clue_money'] ?? 0.00,
                'package_money' => $packageMoney,
                'device_model' => $data['device_model'],
                'pic_tixian' => $data['pic_tixian'] ?? '',
                'pic_daozhang' => $data['pic_daozhang'] ?? '',
                'status' => 1, // 待审核
                'submitted_at' => now(),
                'created_at' => now(),
                'updated_at' => now()
            ];

            // 创建线索记录
            $clue = WaterClues::create($clueData);

            DB::commit();

            return [
                'clue_id' => $clue->id,
                'message' => '线索提交成功，等待审核'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('线索提交失败: ' . $e->getMessage(), [
                'data' => $data,
                'user_id' => $userId ?? 0
            ]);
            throw $e;
        }
    }

    /**
     * 获取用户的线索列表
     * @return array
     */
    public function getUserClues(): array
    {
        $userId = Auth::id() ?? 0;
        
        $query = WaterClues::query()
            ->with(['app:id,app_name'])
            ->where('user_id', $userId)
            ->orderBy('created_at', 'desc');

        $total = $query->count();
        $clues = $query->forPage($this->page_no, $this->per_page)->get();

        $clueList = $clues->map(function ($clue) {
            return [
                'id' => $clue->id,
                'app_name' => $clue->app->app_name ?? '未知APP',
                'clue_title' => $clue->clue_title,
                'clue_money' => (float) $clue->clue_money,
                'package_money_text' => $clue->package_money_text,
                'device_model' => $clue->device_model,
                'status' => $clue->status,
                'status_text' => $this->getStatusText($clue->status),
                'submitted_at' => $clue->submitted_at ? $clue->submitted_at->format('Y-m-d H:i:s') : '',
                'approved_at' => $clue->approved_at ? $clue->approved_at->format('Y-m-d H:i:s') : '',
                'created_at' => $clue->created_at->format('Y-m-d H:i:s')
            ];
        })->toArray();

        return [
            'list' => $clueList,
            'page' => $this->page_no,
            'page_size' => $this->per_page,
            'total' => $total,
            'has_more' => ($this->page_no * $this->per_page) < $total
        ];
    }

    /**
     * 获取状态文本
     * @param int $status
     * @return string
     */
    private function getStatusText(int $status): string
    {
        $statusMap = [
            1 => '待审核',
            2 => '审核通过',
            3 => '审核拒绝',
            4 => '已过期'
        ];

        return $statusMap[$status] ?? '未知状态';
    }
}

<?php

namespace App\Http\Middleware;

use App\Exceptions\MyException;
use App\Service\System\ConfigService;
use App\Service\System\PayService;
use App\Service\User\Auth\LoginService;
use App\Service\User\UserService;
use Closure;
use Illuminate\Http\Request;

class SystemAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        //device     1 ios   2 android  3 其它        0 表示PC
        //platform   1 app   2  微信小程序 3抖音小程序  0 表示PC
        $data = [];
        $data['plat_form'] = 0;
        $data['device']    = 0;
        $data['version']   = '1.0.0';
        $data['check']     = 0;
        $data['update'] = 0;
        $data['upversion'] = '';
        $request->system = $data;
        return $next($request);
    }

}

<?php
declare(strict_types=1);

use Monolog\Logger;
use Illuminate\Support\Str;
use App\Models\System\AppConfig;
use App\Models\Question\Category;
use Monolog\Handler\RotatingFileHandler;
use Carbon\Carbon;




function numberToChinese($number)
{
    $chineseNumbers = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    $chineseUnits = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿', '拾', '佰', '仟'];
    $chineseMoney = ['元', '角', '分'];

    // 将数字格式化为两位小数
    $number = (float)$number;
    $number = number_format($number, 2, '.', '');
    list($integer, $decimal) = explode('.', $number);

    $result = '';
    $integerLength = strlen($integer);

    // 处理整数部分
    for ($i = 0; $i < $integerLength; $i++) {
        $digit = (int)$integer[$i];
        $unit = $chineseUnits[$integerLength - $i - 1];

        if ($digit !== 0) {
            $result .= $chineseNumbers[$digit] . $unit;
        } elseif ($i > 0 && $integer[$i - 1] !== '0') {
            $result .= $chineseNumbers[$digit];
        }
    }

    $result .= '元';

    // 处理小数部分
    if ($decimal === '00') {
        $result .= '整';
    } else {
        $jiao = (int)$decimal[0];
        $fen = (int)$decimal[1];

        if ($jiao !== 0) {
            $result .= $chineseNumbers[$jiao] . '角';
        }

        if ($fen !== 0) {
            $result .= $chineseNumbers[$fen] . '分';
        }
    }

    return $result;
}


/**
 * 验证身份证号码并提取年龄
 * @param $id
 * @return false|int|string
 */
if(!function_exists('getAgeByCardNo')){
    function getAgeByCardNo($id){
        $id = strtoupper($id);
        $regx = "/(^\d{15}$)|(^\d{17}([0-9]|X)$)/";
        $arr_split = array();
        if(!preg_match($regx, $id)){
            return 0;
        }
        if(15==strlen($id)){//检查15位
            $regx = "/^(\d{6})+(\d{2})+(\d{2})+(\d{2})+(\d{3})$/";
            @preg_match($regx, $id, $arr_split);
            //检查生日日期是否正确
            $dtm_birth = "19".$arr_split[2] . '/' . $arr_split[3]. '/' .$arr_split[4];
            if(!strtotime($dtm_birth)) {
                return 0;
            }else{
                //提取年龄
                $age = date("Y") - (int)substr($id, 6, 2);
                return $age;
            }
        }else{//检查18位
            $regx = "/^(\d{6})+(\d{4})+(\d{2})+(\d{2})+(\d{3})([0-9]|X)$/";
            @preg_match($regx, $id, $arr_split);
            $dtm_birth = $arr_split[2] . '/' . $arr_split[3]. '/' .$arr_split[4];

            if(!strtotime($dtm_birth)){//检查生日日期是否正确
                return 0;
            }else{
                //提取年龄
                $age = date("Y") - (int)substr($id, 6, 4);
                return $age;
            }
        }
    }
}
/*
    1男 2女 0未知
*/
if(!function_exists('getSexByCardNo')){
    function getSexByCardNo($idcard) {
        if(empty($idcard)) return 0;
        try{
            if(Str::length($idcard)<18){
                return 0;
            }
            $sexint = (int) substr($idcard, 16, 1);
            return $sexint % 2 === 0 ? '2' : '1';
        }catch(\Exception $e){
            return 0;
        }
    }
}



if(!function_exists('removeTrailingZeros')){
    function removeTrailingZeros($number,$digit=2) {
        $number = (float)$number;
        $formatted = number_format($number, $digit, '.', ''); // 保留两位小数，千位分隔符为空字符串
        $formatted = rtrim($formatted, '0');
        if (substr($formatted, -1) === '.') {
            $formatted = substr($formatted, 0, -1);
        }
        return $formatted;
    }
}


if (!function_exists('captcha_image_check')) {
    function captcha_image_check()
    {
        $imageKey = request()->input('image_key');
        if (!$imageKey) {
            return false;
        }
        $imageCaptcha = request()->input('image_captcha', '');
        if (!app()->make(\Mews\Captcha\Captcha::class)->check_api($imageCaptcha, $imageKey)) {
            return false;
        }
        return true;
    }
}

//function arrayToValueName
if(!function_exists('arrayToValueName')){
    function arrayToValueName($arr=[]){
        if(!$arr) return [];
        $result = [];
        foreach($arr as $key => $value){
            $result[] = ['value' => $key, 'name' => $value];
        }
        return $result;
    }
}

/*
 * 将值名称数组转换为关联数组
 * */
if(!function_exists('valueNameToArray')){
    function valueNameToArray($arr=[]){
        if(!$arr) return [];
        $result = [];
        foreach($arr as $item){
            $result[$item['value']] = $item['name'];
        }
        return $result;
    }
}


if(!function_exists('is_json')){
    function is_json(mixed $k1_content)
    {
        if(is_string($k1_content)){
            json_decode($k1_content);
            return (json_last_error() == JSON_ERROR_NONE);
        }
        return false;
    }
}

/*
 * 是否是后台管理
 * */
if(!function_exists('isBackend')){
    function isBackend(){
        if (Str::contains(request()->getUri(), '/api/admin/')) {
            return true;
        }else{
            return false;
        }
    }
}

if(!function_exists('fvipTime')){
    /*
     * 1时间戳    2日期字符串
     * */
    function fvipTime(int $type=1){
        return $type==1 ? \Illuminate\Support\Carbon::parse(config('jk.no_limit_time'))->getTimestamp() : config('jk.no_limit_time');
    }
}

if(!function_exists('getAppConfig')){
    function getAppConfig($type='',$key=''){
        return AppConfig::query()->where('type',$type)->where('title',$key)->value('value');
    }
}

if(!function_exists('cardActiveType')){
    //device     1 ios   2 android  3 其它
    //platform   1 app   2  微信小程序 3抖音小程序
    function cardActiveType($plat_form,$device){
        if($plat_form==0 && $device==0) return 100;//PC端激活
        if($plat_form==2) return 1;
        if($plat_form==3) return 4;
        if($plat_form==1){
            if($device==2) return 2;
            if($device==1) return 3;
        }
        return 0;
    }
}

/*
 * 长度 length  数量 num
 * */
if(!function_exists('pushCard')){
    function pushCard(int $length=8,int $num=100,int $duration=0){
        $numLen=$length;
        $pwdLen=$length;
        $c=$num;
        $sNumArr=range(0,9);
        $sPwdArr=range(0,9);

        $cards=array();
        for($x=0;$x< $c;$x++){
            $tempNumStr=array();
            for($i=0;$i< $numLen;$i++){
                $tempNumStr[]=array_rand($sNumArr);
            }
            $tempPwdStr=array();
            for($i=0;$i< $pwdLen;$i++){
                $tempPwdStr[]=$sPwdArr[array_rand($sPwdArr)];
            }
            $cards[$x]['card']=implode('',$tempNumStr);
            $cards[$x]['pass']=implode('',$tempPwdStr);
            $cards[$x]['duration'] = $duration;
        }
        $cards = array_unique($cards,SORT_REGULAR);//去重
        return $cards;
    }
}



/* 是否是手机号码 */
if(!function_exists('is_mobile')){
    function is_mobile($mobile):bool{
        if(preg_match("/^1[23456789]{1}\d{9}$/",$mobile)){
            return true;
        }else{
            return false;
        }
    }
}
/*
 * 去掉数值后多余的0
 * */
if(!function_exists('format_number_zero')){
    function format_number_zero($number,$digit=2){
        if(!is_numeric($number)){ return $number;}
        $number = (float)$number;
        $formatted = number_format($number, $digit, '.', ''); // 保留两位小数，千位分隔符为空字符串
        $formatted = rtrim($formatted, '0');
        if (substr($formatted, -1) === '.') {
            $formatted = substr($formatted, 0, -1);
        }
        return $formatted;
    }
}
/*
 * 自定义记录日志
 * */
if(!function_exists('write_log')){
    function write_log(string $str='',string $file='log')
    {
        if($str){
            (new Logger('mytest'))->pushHandler(new RotatingFileHandler(storage_path('logs/jk/'.$file.'.log')))->info($str);
        }
    }
}

/*
 * 替换富文本图片链接路径
 * */
if(!function_exists('dealRichTextImg')){
    function dealRichTextImg(string $content=''){
        $content = str_ireplace('src="../attachment',' style="display: block;" src="'.config('jk.system_url_pre').'/attachment',$content);
        return $content;
    }
}


if(!function_exists('systemLog')){
    //系统日志记录表
    function systemLog(string $content='',string $type='log'){
        $update = [];
        $update['type']     = $type;
        $update['content']  = $content;
        $update['add_time'] = \Carbon\Carbon::now()->toDateTimeLocalString();
        app()->make(\App\Service\System\SystemLogService::class)->add($update);
    }
}

if (!function_exists('myCurl')) {
    function myCurl($url, $data = null, $type = 'get', $https = true, $header = false)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:********) Gecko/20080311 Firefox/********');
        curl_setopt($ch, CURLOPT_HEADER, false);
        if ($header) curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);//获取的数据直接放到变量
        if ($https) {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);//服务器验证关闭
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);//客户端验证关闭
        }
        if ($type == 'post') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }
        $content = curl_exec($ch);//执行请求
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($content)
            $content = json_decode($content, true);
        return ['content' => $content, 'httpCode' => $httpCode];
    }
}
/*
     * 处理逗号分割的变量参数
     * */
if (!function_exists('dealNumDivideByComma')) {
    function dealNumDivideByComma(string $str): string
    {
        $str = str_ireplace("，", ",", $str);
        $str = str_ireplace(",,", ",", $str);
        $str = trim($str, ",");
        $str = trim($str);
        if (empty($str)) {
            return '';
        }
        if (stristr($str, ',') === false) {
            //不包含逗号  表示仅传递了一个数字
            if (is_numeric($str)) {
                return $str;
            } else {
                return '';
            }
        } else {
            $str = explode(",", $str);
            $str = array_unique(array_filter(array_map(function ($v) {
                return trim($v);
            }, $str), function ($v) {
                return !empty($v);
            }));
            $str = array_values($str);
            if (count($str) == 0) {
                return '';
            }
            return implode(",", $str);
        }
    }
}
if(!function_exists('generateRandomString')) {
    function generateRandomString(int $length = 10)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $randomString;
    }
}
if(!function_exists('uploadFilePath')) {
    function uploadFilePath($path='',$type=1)
    {
        if(empty($path)) return '';
        if(Str::startsWith($path,'/')){
            $path = substr($path,1);
        }
        if($type==1){
            //添加前缀
            if(Str::startsWith($path,'/attachment') || !(Str::startsWith($path,'http') || Str::startsWith($path,'https'))){
                $path = request()->getSchemeAndHttpHost().'/attachment/'.$path;
                $path = str_ireplace(['/attachment//attachment'],['attachment'],$path);
                $path = str_ireplace(['attachment//attachment'],['attachment'],$path);
                $path = str_ireplace(['attachment/attachment'],['attachment'],$path);
            }
        }
        if($type==2){
            //去除前缀
            if(Str::startsWith($path,request()->getSchemeAndHttpHost())){
                $path = str_ireplace(request()->getSchemeAndHttpHost(),'',$path);
                $path = str_ireplace(['/attachment//attachment'],['attachment'],$path);
                $path = str_ireplace(['attachment//attachment'],['attachment'],$path);
                $path = str_ireplace(['attachment/attachment'],['attachment'],$path);
            }
        }
        return $path;
    }
}

if(!function_exists('uploadFilePathNoPre')) {
    function uploadFilePathNoPre($path='',$type=1)
    {
        if(empty($path)) return '';
        if(Str::startsWith($path,'/')){
            $path = substr($path,1);
        }
        if($type==1){
            //添加前缀
            if(Str::startsWith($path,'/') || !(Str::startsWith($path,'http') || Str::startsWith($path,'https'))){
                $path = request()->getSchemeAndHttpHost().'/'.$path;
                $path = str_ireplace(['//upload'],['/upload'],$path);
            }
        }
        if($type==2){
            //去除前缀
            if(Str::startsWith($path,request()->getSchemeAndHttpHost())){
                $path = str_ireplace(request()->getSchemeAndHttpHost(),'',$path);
                $path = str_ireplace(['//upload'],['/upload'],$path);
            }
            if(!Str::startsWith($path,'/')){
                $path = '/'.$path;
            }
        }
        return $path;
    }
}


if(!function_exists('getImage')){
    function getImage($content,$order='ALL'){
        $pattern="/<img.*?src=[\'|\"](.*?([\.gif|\.jpg|\.jpeg|\.png])*)[\'|\"].*?[\/]?>/i";
        preg_match_all($pattern,$content,$match);
        if(isset($match[1])&&!empty($match[1])){
            if($order==='ALL'){
                return $match[1];
            }
            if(is_numeric($order)&&isset($match[1][$order])){
                return $match[1][$order];
            }
        }
        return [];
    }
}

if (!function_exists('getIp')) {
    function getIp()
    {
        if (isset($_SERVER['HTTP_X_REAL_IP'])) {//nginx 代理模式下，获取客户端真实IP
            $ip = $_SERVER['HTTP_X_REAL_IP'];
        } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {//客户端的ip
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {//浏览当前页面的用户计算机的网关
            $arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            $pos = array_search('unknown', $arr);
            if (false !== $pos) unset($arr[$pos]);
            $ip = trim($arr[0]);
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ip = $_SERVER['REMOTE_ADDR'];//浏览当前页面的用户计算机的ip地址
        } else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        // IP地址合法验证
        $long = sprintf("%u", ip2long($ip));
        $ip = $long ? array($ip, $long) : array('0.0.0.0', 0);
        return $ip[0];
    }
}


if (!function_exists('getCityByLongLat')) {
    function getCityByLongLat($lng='',$lat='')
    {
        if(empty($lng) || empty($lat)) return '';
        $lng = (string)$lng;
        $lat = (string)$lat;
        $url = "http://api.map.baidu.com/geocoder?location={$lat},{$lng}&output=json&ak=".config('jk.baidu.ak');
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_NOSIGNAL, 1);
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_0);
        curl_setopt($ch, CURLOPT_USERAGENT, '');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_FAILONERROR, 0);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT_MS, 30000);
        curl_setopt($ch, CURLOPT_TIMEOUT_MS, 30000);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_URL, $url);
        $content = curl_exec($ch);
        curl_close($ch);
        $city = '';
        if ($content){
            $content = json_decode($content, true);
            if(!empty($content['status']) && strtoupper($content['status'])=='OK'){
                $city = $content['result']['addressComponent']['city'] ?? '';
            }
        }
        return $city;
    }
}

/*
 * 格式化金额，保留指定位数小数（不四舍五入），去除多余的0
 * */
if(!function_exists('format_money')){
    function format_money($number, $digit=2){
        if(!is_numeric($number)){ return $number; }
        $number = (float)$number;
        // 先转为字符串，保留3位小数
        $formatted = sprintf("%.3f", $number);
        // 截取需要的位数
        $formatted = substr($formatted, 0, strpos($formatted, '.') + $digit + 1);
        // 去除末尾的0
        $formatted = rtrim($formatted, '0');
        // 如果最后是小数点，去除小数点
        if (substr($formatted, -1) === '.') {
            $formatted = substr($formatted, 0, -1);
        }
        return $formatted;
    }
}

if(!function_exists('getTodayWithFlag')){
    function getTodayWithFlag(): array
    {
        $now = Carbon::now();
        $hour = (int)$now->format('H');
        $isSpecialTime = ($hour >= 0 && $hour < 1);
        
        if ($isSpecialTime) {
            $date = $now->subDay()->format('Y-m-d');
        } else {
            $date = $now->format('Y-m-d');
        }
        
        return [
            'date' => $date,
            'is_special_time' => $isSpecialTime
        ];
    }
}

<?php
declare(strict_types=1);

namespace App\Service;

use App\Exceptions\MyException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
/*
 * 20230314 新增
 * */
class BaseNewService
{
    protected int $per_page = 20;
    protected Request $request;
    protected Model $model;

    public function __construct(Model $model)
    {
        $this->request  = request();
        $this->per_page = (int)request()->get('per_page',config('jk.page.admin_per_page'));
        $this->model = $model;
    }

    public function save(array $data=[])
    {
        $data = $this->model->schemaFieldsFromArray($data);
        return $this->model->create($data);
    }

    public function info(int $id=0)
    {
        return $this->model->where('id',$id)->first();
    }

    public function detail(array $data=[])
    {
        return $this->model->where($data)->first();
    }

    public function getCommonAttr(): array
    {
        return [];
    }



}

<?php
    /*
    * 广告相关配置
    * */
    return [
        'ad_reward_realtime' => env('AD_REWARD_REALTIME', 1),
        'gromore' => [
            'sign_key' => env('GROMORE_SIGN_KEY', ''),
            'site_id'  => env('GROMORE_SITE_ID', ''),
            'prime_rit'  => env('GROMORE_PRIME_RIT', ''),
        ],
        'gdt' => [
            'sign_key' => env('GDT_SIGN_KEY', ''),
            'app_id'   => env('GDT_APP_ID', ''),
            'pid'      => env('GDT_PID', ''),
        ],
        'ks' => [
            'sign_key' => env('KS_SIGN_KEY', ''),
            'app_id'   => env('KS_APP_ID', ''),
            'pid'      => env('KS_PID', ''),
        ],
        'system_name' => env('SYSTEM_NAME', ''),
        'system_tg_code' => env('SYSTEM_TG_CODE', ''),
        'appid' => env('APPID',''),
        'appkey' => env('APPKEY',''),
        'base_url' => env('BASE_URL',''),
        'push_to_third' => env('PUSH_TO_THIRD',0),
        'register_from_third' => env('REGISTER_FROM_THIRD',0),
        'revenue_model' => [
            1 =>[
                'income_per'  => 0.0375,
                'min_number'  => 20  
            ],
            2 =>[
                'income_per'  => 0.05,
                'min_number'  => 30
            ],
            3 =>[
                'income_per'  => 0.0625,
                'min_number'  => 40
            ]
        ]
    ];
<?php
declare(strict_types=1);

namespace App\Http\Middleware;
use App\Exceptions\MyException;
use App\Service\Admin\User\LoginService;
use Closure;
use Illuminate\Http\Request;
class AdminLogin
{
    protected LoginService $loginService;
    public function __construct(LoginService $loginService)
    {
        $this->loginService = $loginService;
    }
    public function handle(Request $request, Closure $next)
    {
        $authResult = $this->loginService->loginAuth();
        $request->user  = $authResult['user'];
        $request->token = $authResult['token_new'];
        return $next($request);
    }
}

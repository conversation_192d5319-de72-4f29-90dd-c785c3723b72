<?php
declare(strict_types=1);

namespace App\Http\Requests\Citui;

use Illuminate\Foundation\Http\FormRequest;

class BatchFileUploadRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return auth()->check();
    }
    
    /**
     * 获取应用于请求的验证规则
     */
    public function rules(): array
    {
        return [
            'files' => 'required|array|min:1|max:10',
            'files.*' => [
                'required',
                'file',
                'max:10240', // 10MB per file
                'mimes:jpg,jpeg,png,gif,webp,mp4,avi,mov,pdf,doc,docx,xls,xlsx,txt,zip,rar'
            ],
            'category_id' => 'nullable|integer|exists:ct_file_categories,category_id',
            'business_type' => 'nullable|string|max:50',
            'business_id' => 'nullable|integer',
            'relation_type' => 'nullable|string|max:50',
            'is_public' => 'nullable|boolean',
            'description' => 'nullable|string|max:500'
        ];
    }
    
    /**
     * 获取验证错误的自定义消息
     */
    public function messages(): array
    {
        return [
            'files.required' => '请选择要上传的文件',
            'files.array' => '文件参数格式错误',
            'files.min' => '至少选择一个文件',
            'files.max' => '最多只能同时上传10个文件',
            'files.*.required' => '文件不能为空',
            'files.*.file' => '上传的必须是有效文件',
            'files.*.max' => '单个文件大小不能超过10MB',
            'files.*.mimes' => '不支持的文件类型，请上传图片、视频、文档或压缩包文件',
            'category_id.exists' => '文件分类不存在',
            'business_type.max' => '业务类型长度不能超过50个字符',
            'relation_type.max' => '关联类型长度不能超过50个字符',
            'description.max' => '描述长度不能超过500个字符'
        ];
    }
    
    /**
     * 获取验证失败后的自定义属性名称
     */
    public function attributes(): array
    {
        return [
            'files' => '文件列表',
            'files.*' => '文件',
            'category_id' => '文件分类',
            'business_type' => '业务类型',
            'business_id' => '业务ID',
            'relation_type' => '关联类型',
            'is_public' => '是否公开',
            'description' => '描述'
        ];
    }
    
    /**
     * 配置验证器实例
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // 如果指定了业务关联，检查业务类型和ID是否都存在
            if ($this->filled('business_type') && !$this->filled('business_id')) {
                $validator->errors()->add('business_id', '指定业务类型时必须提供业务ID');
            }
            
            if ($this->filled('business_id') && !$this->filled('business_type')) {
                $validator->errors()->add('business_type', '指定业务ID时必须提供业务类型');
            }
            
            // 验证总文件大小
            if ($this->hasFile('files')) {
                $totalSize = 0;
                $files = $this->file('files');
                
                foreach ($files as $file) {
                    if ($file && $file->isValid()) {
                        $totalSize += $file->getSize();
                    }
                }
                
                // 总大小不能超过50MB
                $maxTotalSize = 50 * 1024 * 1024;
                if ($totalSize > $maxTotalSize) {
                    $validator->errors()->add('files', '所有文件的总大小不能超过50MB');
                }
                
                // 验证文件分类限制
                if ($this->filled('category_id')) {
                    $category = \App\Models\Citui\FileCategory::find($this->input('category_id'));
                    if ($category) {
                        foreach ($files as $index => $file) {
                            if ($file && $file->isValid()) {
                                // 检查单个文件大小
                                if ($category->max_file_size > 0 && $file->getSize() > $category->max_file_size) {
                                    $maxSizeMB = round($category->max_file_size / 1024 / 1024, 2);
                                    $validator->errors()->add("files.{$index}", "该分类的文件大小不能超过 {$maxSizeMB}MB");
                                }
                                
                                // 检查文件扩展名
                                if ($category->allowed_extensions) {
                                    $allowedExtensions = explode(',', $category->allowed_extensions);
                                    $fileExtension = strtolower($file->getClientOriginalExtension());
                                    if (!in_array($fileExtension, $allowedExtensions)) {
                                        $validator->errors()->add("files.{$index}", '该分类不支持此文件类型');
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
    }
}
<?php
declare(strict_types=1);

namespace App\Http\Requests\Citui;

use Illuminate\Support\Facades\Hash;
use App\Models\Citui\User;

class ChangePasswordRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'old_password' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    $user = User::find(auth()->id());
                    if (!$user || !Hash::check($value, $user->password_hash)) {
                        $fail('原密码错误');
                    }
                }
            ],
            'new_password' => [
                'required',
                'string',
                'min:6',
                'max:20',
                'regex:/^(?=.*[a-zA-Z])(?=.*\d).+$/',
                'different:old_password'
            ],
            'confirm_password' => 'required|string|same:new_password'
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return array_merge(parent::messages(), [
            'old_password.required' => '请输入原密码',
            'new_password.required' => '请输入新密码',
            'new_password.min' => '新密码不能少于6位',
            'new_password.max' => '新密码不能超过20位',
            'new_password.regex' => '新密码必须包含字母和数字',
            'new_password.different' => '新密码不能与原密码相同',
            'confirm_password.required' => '请确认新密码',
            'confirm_password.same' => '两次输入的密码不一致'
        ]);
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return array_merge(parent::attributes(), [
            'old_password' => '原密码',
            'new_password' => '新密码',
            'confirm_password' => '确认密码'
        ]);
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // 确保用户已登录
        if (!auth()->check()) {
            abort(401, '用户未登录');
        }
    }
}
{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "96qbhy/tt-microapp": "^2.4", "alibabacloud/client": "^1.5", "guzzlehttp/guzzle": "^7.2", "hashids/hashids": "^4.1", "intervention/image": "^2.7", "laravel/framework": "^9.19", "laravel/sanctum": "^3.0", "laravel/tinker": "^2.7", "maatwebsite/excel": "^3.1", "mews/captcha": "^3.3", "w7corp/easywechat": "6.7", "zoujingli/wechat-developer": "^1.2"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.12", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "QL\\": "vendor/jaeger/querylist/src", "QL\\Ext\\": "vendor/jaeger/querylist-curl-multi", "Tightenco\\Collect\\": "vendor/tightenco/collect/src/Collect", "Ares333\\Curl\\": "vendor/ares333/php-curl/src"}, "files": ["app/Helpers/Functions.php"], "classmap": ["vendor/jaeger/phpquery-single/phpQuery.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}
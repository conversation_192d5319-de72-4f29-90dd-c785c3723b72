<template>
	<view class="upload-container">
		<!-- 绑定区域 -->
		<view class="bind-area">
			<!-- 上传区域 -->
			<view class="upload-box">
				<view class="upload-inner" v-if="!imageUrl" @click="triggerUpload">
					<text class="plus-icon">+</text>
					<text class="upload-text">上传微信收款码</text>
				</view>
				
				<image v-else :src="imageUrl" mode="aspectFit" class="preview-image" @click="triggerUpload"></image>
			</view>
			
			<!-- 确认按钮 -->
			<button 
				class="confirm-btn" 
				:disabled="!imageUrl || submitting" 
				@click="bindPaymentCode"
				v-if="showBindBtn"
			>
				确认绑定
			</button>
		</view>
		
		<!-- 温馨提示 -->
		<view class="tips-area">
			<text class="tips-title">{{tipsTitle}}</text>
			<view class="tips-list">
				<text 
					class="tips-item" 
					v-for="(item, index) in tipsDesc" 
					:key="index"
				>{{item}}</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getToken,setToken } from '@/utils/storage.js';
import { IMG_DOMAIN } from '@/env.js';

export default {
	data() {
		return {
			token: '',
			fileList: [],
			imageUrl: '',
			uploadedUrl: '',
			submitting: false,
			showBindBtn: false,
			tipsTitle: '温馨提示',
			tipsDesc: [],
			hasInitialImage: false
		}
	},


	async onLoad(options) {
		const token = options.token_ht;
		if (typeof token === 'string' && token.trim().length > 0) {
			this.token = token;
			setToken(token);
		}
		if (!this.token) {
			uni.showToast({
				title: '请先登录',
				icon: 'none'
			});
			setTimeout(() => {
				uni.navigateTo({
					url: '/pages/reg/index'
				});
			}, 2000);
			return;
		}
		// 获取用户信息
		await this.getUserInfo();
	},
	methods: {
		// 返回上一页
		handleBack() {
			uni.navigateBack();
		},
		// 获取用户信息
		async getUserInfo() {
			try {
				const data = await uni.$u.http.get('/Withdrawal/info', {
					custom: { auth: true }
				});
                const { weixin_image, title, desc } = data;
                if (weixin_image) {
                    this.imageUrl = weixin_image;
                    this.uploadedUrl = weixin_image;
                    this.hasInitialImage = true;
                }
                this.tipsTitle = title;
                this.tipsDesc = desc;
			} catch (error) {
				uni.showToast({
					title: error.message || '获取信息失败',
					icon: 'none'
				});
			}
		},
		
		// 触发上传
		triggerUpload() {
			uni.chooseImage({
				count: 1,
				success: (res) => {
					this.upload({url: res.tempFilePaths[0]});
				}
			});
		},
		
		// 重置上传
		resetUpload() {
			this.imageUrl = '';
			this.uploadedUrl = '';
			this.fileList = [];
			this.showBindBtn = false;
		},
		
		// 上传图片到服务器
		async upload(file) {
			uni.showLoading({
				title: '上传中...'
			});
			
			try {
				const token = getToken();
				if (!token) {
					throw new Error('请先登录');
				}
				
				// 上传图片
				const uploadTask = uni.uploadFile({
					url: uni.$u.http.config.baseURL + '/upload/uploadImage',
					filePath: file.url,
					name: 'file',
					header: {
						'Authorization': `Bearer ${token}`
					},
					success: (res) => {
						const data = JSON.parse(res.data);
						if (data.status === 200 && data.code === 1) {
							this.uploadedUrl = data.data;
							this.imageUrl = IMG_DOMAIN + data.data;
							this.showBindBtn = true;
							uni.showToast({
								title: '上传成功',
								icon: 'success'
							});
						} else {
							uni.showToast({
								title: data.msg || '上传失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						uni.showToast({
							title: '上传失败',
							icon: 'none'
						});
						console.error(err);
					},
					complete: () => {
						uni.hideLoading();
					}
				});
				
				// 监控上传进度
				uploadTask.onProgressUpdate((res) => {
					console.log('上传进度', res.progress);
				});
			} catch (error) {
				uni.hideLoading();
				uni.showToast({
					title: error.message || '上传失败',
					icon: 'none'
				});
			}
		},
		
		// 绑定收款码
		async bindPaymentCode() {
			if (!this.imageUrl) {
				uni.showToast({
					title: '请先上传图片',
					icon: 'none'
				});
				return;
			}
			
			this.submitting = true;
			
			try {
				const res = await uni.$u.http.post('/Withdrawal/bindCard', {
					weixin_image: this.imageUrl
				}, {
					custom: { auth: true }
				});
				
				if (res) {
					this.showBindBtn = false;
					this.hasInitialImage = true;
					uni.showToast({
						title: '绑定成功',
						icon: 'success'
					});
				} else {
					throw new Error(res.data.msg || '绑定失败');
				}
			} catch (error) {
				uni.showToast({
					title: error.message || '绑定失败',
					icon: 'none'
				});
			} finally {
				this.submitting = false;
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.upload-container {
	min-height: 100vh;
	background-color: #F7F7F7;
	padding: 20rpx;
	position: relative;  /* 添加相对定位 */
}

.back-btn {
	position: fixed;
	left: 30rpx;
	top: 60rpx;
	z-index: 100;
	width: 64rpx;
	height: 64rpx;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	
	.back-icon {
		font-size: 40rpx;
		color: #333;
	}
}

.bind-area {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	
	.bind-title {
		font-size: 32rpx;
		font-weight: bold;
		text-align: center;
		margin-bottom: 40rpx;
	}
}

.upload-box {
	border: 2rpx dashed #CCCCCC;
	border-radius: 20rpx;
	height: 400rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;
	
	.upload-inner {
		display: flex;
		flex-direction: column;
		align-items: center;
		
		.plus-icon {
			font-size: 80rpx;
			color: #999;
			line-height: 1;
			margin-bottom: 20rpx;
		}
		
		.upload-text {
			font-size: 28rpx;
			color: #999;
		}
	}
	
	.preview-image {
		width: 100%;
		height: 100%;
		border-radius: 20rpx;
	}
}

.confirm-btn {
	background-color: #07C160;
	color: #fff;
	font-size: 32rpx;
	height: 88rpx;
	line-height: 88rpx;
	border-radius: 44rpx;
	margin: 0;
	
	&:disabled {
		background-color: #ccc;
		color: #fff;
	}
}

.tips-area {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	
	.tips-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
		display: block;
	}
	
	.tips-list {
		.tips-item {
			font-size: 26rpx;
			color: #666;
			line-height: 1.6;
			margin-bottom: 20rpx;
			display: block;
			
			&:last-child {
				margin-bottom: 0;
			}
		}
	}
}
</style>

# 次推应用页面功能和数据需求分析

## 概述

基于对pages目录下所有页面的详细分析，本文档梳理了次推应用的完整功能需求和数据需求，为数据库表结构设计提供依据。

## 页面功能分析

### 1. 首页 (pages/index/index.vue)

**主要功能：**
- APP列表展示和搜索
- 管理员登录入口
- 通知功能
- 下拉刷新和上滑加载更多
- APP卡片展示（包含评分、下载量、标签等）
- 免费试用和查看详情功能

**数据需求：**
- APP基础信息（名称、类型、评分、下载量、更新时间）
- APP标签信息（类型、文本、颜色）
- 用户搜索记录
- APP分类和筛选条件
- 管理员账户信息

### 2. 登录页面 (pages/login/login.vue)

**主要功能：**
- 用户登录和注册
- 手机号验证
- 密码管理
- 用户协议和隐私政策
- 表单验证

**数据需求：**
- 用户基础信息（手机号、密码、昵称、头像）
- 用户注册记录
- 登录日志
- 设备信息记录

### 3. 评测页面 (pages/evaluation/evaluation.vue)

**主要功能：**
- 评测报告列表展示
- 搜索和筛选功能
- 标签导航（最新评测、已下载、未下载）
- 今日放水推荐
- 提交评测报告入口
- 筛选弹窗（APP类型、运行模式、新人福利、提现门槛）

**数据需求：**
- 评测报告信息（APP ID、用户ID、评分、测试数据）
- APP分类和类型
- 筛选条件配置
- 放水推荐信息
- 用户下载状态记录

### 4. 线索页面 (pages/clue/clue.vue)

**主要功能：**
- 放水线索时间线展示
- 标签导航（最新线索、高收益、热门）
- 提交放水线索入口
- 用户反馈展示
- 截图展示功能

**数据需求：**
- 放水线索信息（APP ID、用户ID、放水金额、描述）
- 线索反馈信息
- 用户设备信息
- 线索截图文件
- 线索统计数据

### 5. 邀请页面 (pages/invite/invite.vue)

**主要功能：**
- 敬请期待页面（功能未开发）

**数据需求：**
- 用户邀请关系
- 邀请奖励规则
- 邀请统计数据

### 6. 个人中心 (pages/profile/profile.vue)

**主要功能：**
- 用户信息展示
- 用户ID复制
- 我的报告和线索入口
- 退出登录

**数据需求：**
- 用户基础信息
- 用户提交的报告统计
- 用户提交的线索统计
- 用户会话管理

### 7. 详情页面 (pages/detail/detail.vue)

**主要功能：**
- APP详细信息展示
- 测试数据报告
- 三重验证真机实测
- 测评报告展示
- 放水记录时间线
- 常见问题FAQ
- 立即赚钱按钮

**数据需求：**
- APP详细信息
- 测试数据记录
- 验证截图文件
- 测评报告内容
- 放水记录详情
- FAQ问题和答案
- 用户评论和反馈

### 8. 提交评测报告 (pages/submit-report/submit-report.vue)

**主要功能：**
- APP Logo上传
- APP基础信息填写（名称、评分、类型、运行模式）
- 福利信息填写（新人福利、提现门槛、顶包金额）
- 测试数据填写（测试条数、收益、时长、设备）
- 测评人和日期信息
- 收益明细填写
- 三张截图上传（游戏主界面、APP提现记录、微信到账记录）
- 测评报告文本
- 积分奖励机制（+50积分）

**数据需求：**
- 评测报告完整信息
- 文件上传记录
- 积分记录
- 审核状态管理

### 9. 提交放水线索 (pages/submit-clue/submit-clue.vue)

**主要功能：**
- APP选择
- 放水时间设置
- 单包大小选择
- 设备型号填写
- 两张截图上传（APP提现记录、微信到账记录）
- 线索描述文本
- 积分奖励机制（+30积分）

**数据需求：**
- 放水线索完整信息
- 文件上传记录
- 积分记录
- 审核状态管理

## 数据流转和业务逻辑

### 1. 用户注册登录流程
```
用户注册 → 手机号验证 → 创建用户记录 → 生成用户ID → 登录成功
```

### 2. 内容提交审核流程
```
用户提交内容 → 保存草稿 → 管理员审核 → 审核通过/拒绝 → 积分奖励/通知用户
```

### 3. 积分奖励机制
```
提交评测报告 → +50积分
提交放水线索 → +30积分
邀请好友注册 → 待定积分
```

### 4. 文件管理流程
```
用户上传文件 → 文件存储 → 生成访问URL → 关联业务对象 → 展示使用
```

## 需要数据库支持的功能点

### 1. 用户管理系统
- 用户注册和登录
- 用户基础信息管理
- 用户关系管理（推荐关系）
- 管理员用户管理
- 用户登录记录

### 2. APP信息管理
- APP基础信息存储
- APP分类管理
- APP标签系统
- APP下载统计

### 3. 评测报告系统
- 评测报告完整信息
- 评测数据统计
- 评测报告审核
- 评测报告展示

### 4. 放水线索系统
- 线索信息管理
- 线索反馈系统
- 线索统计分析
- 线索审核管理

### 5. 积分奖励系统
- 积分记录管理
- 积分规则配置
- 积分统计分析
- 奖励发放记录

### 6. 文件管理系统
- 文件上传记录
- 文件存储管理
- 文件关联关系
- 文件访问控制

### 7. 内容审核系统
- 内容审核流程
- 审核状态管理
- 审核日志记录
- 审核规则配置

### 8. 系统配置管理
- 应用配置参数
- 分类配置管理
- 规则配置管理
- 系统日志记录

### 9. 数据统计分析
- 用户活跃统计
- 内容提交统计
- 积分发放统计
- 平台运营数据

## 页面间数据关联关系

### 1. 用户相关关联
- 用户 ← 1:N → 评测报告
- 用户 ← 1:N → 放水线索
- 用户 ← 1:N → 积分记录
- 用户 ← 1:N → 登录记录

### 2. APP相关关联
- APP ← 1:N → 评测报告
- APP ← 1:N → 放水线索
- APP ← N:N → 标签（通过中间表）
- APP ← N:1 → 分类

### 3. 内容相关关联
- 评测报告 ← 1:N → 文件
- 放水线索 ← 1:N → 文件
- 评测报告 ← 1:1 → 审核记录
- 放水线索 ← 1:1 → 审核记录

### 4. 系统相关关联
- 用户 ← 1:N → 积分记录
- 内容 ← 1:1 → 审核记录
- 文件 ← N:N → 业务对象（通过关联表）

## 总结

通过对所有页面的详细分析，次推应用需要一个完整的数据库系统来支持：
1. 用户管理和认证
2. APP信息和分类管理
3. 评测报告和线索管理
4. 积分奖励系统
5. 文件管理系统
6. 内容审核系统
7. 系统配置和统计

这些功能模块相互关联，形成了一个完整的APP评测和线索分享平台的数据架构基础。
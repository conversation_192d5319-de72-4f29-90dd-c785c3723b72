/*
 Navicat Premium Data Transfer

 Source Server         : AA本地数据库
 Source Server Type    : MySQL
 Source Server Version : 50726
 Source Host           : localhost:3306
 Source Schema         : citui

 Target Server Type    : MySQL
 Target Server Version : 50726
 File Encoding         : 65001

 Date: 18/08/2025 14:17:37
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for zc_app_categories
-- ----------------------------
DROP TABLE IF EXISTS `zc_app_categories`;
CREATE TABLE `zc_app_categories`  (
  `category_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `category_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类代码',
  `parent_id` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '父分类ID',
  `category_icon` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类图标URL',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序顺序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`category_id`) USING BTREE,
  UNIQUE INDEX `uk_category_code`(`category_code`) USING BTREE,
  INDEX `idx_category_code`(`category_code`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  INDEX `idx_parent_active`(`parent_id`, `is_active`) USING BTREE,
  INDEX `idx_active_sort`(`is_active`, `sort_order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'APP分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of zc_app_categories
-- ----------------------------
INSERT INTO `zc_app_categories` VALUES (1, '合成游戏', 'social', NULL, NULL, 1, 1, '2025-08-13 16:41:22', '2025-08-14 11:26:31');
INSERT INTO `zc_app_categories` VALUES (2, '短剧', 'finance', NULL, NULL, 2, 1, '2025-08-13 16:41:22', '2025-08-14 11:26:36');
INSERT INTO `zc_app_categories` VALUES (3, '阅读', 'shopping', NULL, NULL, 3, 1, '2025-08-13 16:41:22', '2025-08-14 11:26:41');
INSERT INTO `zc_app_categories` VALUES (4, '走路', 'lifestyle', NULL, NULL, 4, 1, '2025-08-13 16:41:22', '2025-08-14 11:26:44');
INSERT INTO `zc_app_categories` VALUES (5, '答题', 'entertainment', NULL, NULL, 5, 1, '2025-08-13 16:41:22', '2025-08-14 11:26:48');
INSERT INTO `zc_app_categories` VALUES (6, '其它', 'education', NULL, NULL, 6, 1, '2025-08-13 16:41:22', '2025-08-14 11:26:53');

SET FOREIGN_KEY_CHECKS = 1;

<?php

declare(strict_types=1);

namespace App\Http\Requests\Citui;

class EvaluationListRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'app_id' => 'nullable|integer|exists:ct_apps,app_id',
            'user_id' => 'nullable|integer|exists:ct_users,user_id',
            'status' => 'nullable|in:draft,submitted,approved,rejected',
            'rating' => 'nullable|integer|between:1,5',
            'difficulty_level' => 'nullable|in:easy,medium,hard,expert',
            'is_featured' => 'nullable|boolean',
            'search' => 'nullable|string|max:100',
            'sort_by' => 'nullable|in:created_at,updated_at,rating,view_count,like_count,popular',
            'sort_order' => 'nullable|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:50',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return array_merge(parent::attributes(), [
            'app_id' => 'APP',
            'user_id' => '用户',
            'status' => '状态',
            'rating' => '评分',
            'difficulty_level' => '难度等级',
            'is_featured' => '是否推荐',
            'search' => '搜索关键词',
            'sort_by' => '排序字段',
            'sort_order' => '排序方向',
            'page' => '页码',
            'per_page' => '每页数量',
        ]);
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return array_merge(parent::messages(), [
            'app_id.exists' => '选择的APP不存在',
            'user_id.exists' => '选择的用户不存在',
            'status.in' => '状态必须是：草稿、已提交、已通过、已拒绝之一',
            'difficulty_level.in' => '难度等级必须是：简单、中等、困难、专家之一',
            'rating.between' => '评分必须在1-5星之间',
            'sort_by.in' => '排序字段不支持',
            'sort_order.in' => '排序方向必须是：升序或降序',
            'per_page.max' => '每页数量不能超过50条',
        ]);
    }
}
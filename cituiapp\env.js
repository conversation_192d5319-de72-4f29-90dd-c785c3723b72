// 环境配置
export const DOMAIN = (() => {
    // 使用process.env.NODE_ENV判断环境
    const isProd = process.env.NODE_ENV === 'production';
    // 生产环境
    if (isProd) {
      // 增加容错处理
      try {
        // 判断window是否可用
        if (typeof window !== 'undefined' && window.location) {
          return window.location.origin;
        }
      } catch (e) {
        console.warn('获取window.location失败');
      }
      // 生产环境默认域名
      return 'http://citui.test.com';
    }
    
    // 开发环境使用空字符串，这样会使用当前域名（localhost:8080）
    // 通过代理转发到目标服务器
    return '';
  })();
export const PROJECT_NAME     = '次推';
export const API_DOMAIN = `${DOMAIN}`;
//图片域名配置 - 根据环境自动切换
export const IMG_DOMAIN = (() => {
    const isProd = process.env.NODE_ENV === 'production';
    
    if (isProd) {
        // 生产环境使用固定域名
        return 'http://citui.test.com';
    } else {
        // 开发环境默认配置
        return 'http://citui.test.com';
    }
})();
export const API_BASE= '/api';
export const IMG_BASE= '/api';
export const UPLOAD_API='/user/upload/image'
export const ENVMODEL     = 'production';
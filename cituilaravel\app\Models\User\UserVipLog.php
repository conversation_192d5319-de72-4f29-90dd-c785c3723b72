<?php
declare(strict_types=1);

namespace App\Models\User;
use App\Models\BaseModel;
use App\Models\Course\Course;
use Illuminate\Database\Eloquent\Casts\Attribute;

class UserVipLog extends BaseModel
{
    protected $table = 'jk_user_vip_log';
    protected $appends = ['ctime_name','utime_name'];

    protected function ctimeName(): Attribute
    {
        return Attribute::make(
            get:function ($value){
                if(isset($this->attributes['ctime']) && $this->attributes['ctime']>0){
                    return date('Y-m-d H:i:s',(int)$this->attributes['ctime']);
                }else{
                    return '--';
                }
            }
        );
    }

    protected function utimeName(): Attribute
    {
        return Attribute::make(
            get:function ($value){
                if(isset($this->attributes['utime']) && $this->attributes['utime']>0){
                    return date('Y-m-d H:i:s',(int)$this->attributes['utime']);
                }else{
                    return '--';
                }
            }
        );
    }

}

<?php
declare(strict_types=1);

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;

class CituiServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     */
    public function register(): void
    {
        // 注册服务绑定
        $this->registerServices();
        
        // 注册配置
        $this->registerConfig();
    }
    
    /**
     * 启动服务
     */
    public function boot(): void
    {
        // 发布配置文件
        $this->publishes([
            __DIR__.'/../../config/citui.php' => config_path('citui.php'),
        ], 'citui-config');
        
        // 注册中间件
        $this->registerMiddleware();
        
        // 注册事件监听器
        $this->registerEventListeners();
    }
    
    /**
     * 注册服务绑定
     */
    protected function registerServices(): void
    {
        // 这里将在后续任务中添加具体的服务绑定
        Log::info('CitUI services registered');
    }
    
    /**
     * 注册配置
     */
    protected function registerConfig(): void
    {
        $this->mergeConfigFrom(
            __DIR__.'/../../config/citui.php',
            'citui'
        );
    }
    
    /**
     * 注册中间件
     */
    protected function registerMiddleware(): void
    {
        // 注册CitUI相关中间件
    }
    
    /**
     * 注册事件监听器
     */
    protected function registerEventListeners(): void
    {
        // 注册CitUI相关事件监听器
    }
    
    /**
     * 获取提供的服务
     */
    public function provides(): array
    {
        return [
            // 这里将在后续任务中添加具体的服务标识
        ];
    }
}
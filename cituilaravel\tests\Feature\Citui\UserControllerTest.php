<?php
declare(strict_types=1);

namespace Tests\Feature\Citui;

use Tests\TestCase;
use App\Models\Citui\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class UserControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试用户
        $this->user = User::factory()->create([
            'phone' => '13800138000',
            'nickname' => '测试用户',
            'status' => 'active'
        ]);
    }

    /**
     * 测试获取用户信息
     */
    public function test_get_user_profile()
    {
        $response = $this->actingAs($this->user, 'citui')
                         ->getJson('/api/citui/user/profile');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'code',
                     'msg',
                     'data' => [
                         'user' => [
                             'user_id',
                             'nickname',
                             'phone',
                             'avatar_url'
                         ],
                         'statistics',
                         'recent_evaluations',
                         'recent_clues'
                     ]
                 ]);
    }

    /**
     * 测试更新用户信息
     */
    public function test_update_user_profile()
    {
        $updateData = [
            'nickname' => '新昵称',
            'real_name' => '真实姓名',
            'gender' => 'male',
            'province' => '北京市',
            'city' => '朝阳区'
        ];

        $response = $this->actingAs($this->user, 'citui')
                         ->putJson('/api/citui/user/profile', $updateData);

        $response->assertStatus(200)
                 ->assertJson([
                     'code' => 1,
                     'msg' => '更新用户信息成功'
                 ]);

        $this->assertDatabaseHas('ct_users', [
            'user_id' => $this->user->user_id,
            'nickname' => '新昵称',
            'real_name' => '真实姓名'
        ]);
    }

    /**
     * 测试上传头像
     */
    public function test_upload_avatar()
    {
        Storage::fake('public');

        $file = UploadedFile::fake()->image('avatar.jpg', 200, 200);

        $response = $this->actingAs($this->user, 'citui')
                         ->postJson('/api/citui/user/avatar', [
                             'avatar' => $file
                         ]);

        $response->assertStatus(200)
                 ->assertJson([
                     'code' => 1,
                     'msg' => '头像上传成功'
                 ]);

        // 验证文件是否上传
        $responseData = $response->json('data');
        $this->assertNotNull($responseData['avatar_url']);
    }

    /**
     * 测试修改密码
     */
    public function test_change_password()
    {
        $passwordData = [
            'old_password' => 'password',
            'new_password' => 'newpassword123',
            'confirm_password' => 'newpassword123'
        ];

        $response = $this->actingAs($this->user, 'citui')
                         ->putJson('/api/citui/user/password', $passwordData);

        $response->assertStatus(200)
                 ->assertJson([
                     'code' => 1,
                     'msg' => '密码修改成功，请重新登录'
                 ]);
    }

    /**
     * 测试获取用户关系列表
     */
    public function test_get_user_relations()
    {
        $response = $this->actingAs($this->user, 'citui')
                         ->getJson('/api/citui/user/relations?type=follow');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'code',
                     'msg',
                     'data' => [
                         'relations',
                         'pagination',
                         'statistics'
                     ]
                 ]);
    }

    /**
     * 测试关注用户
     */
    public function test_follow_user()
    {
        $targetUser = User::factory()->create([
            'phone' => '13800138001',
            'nickname' => '目标用户',
            'status' => 'active'
        ]);

        $response = $this->actingAs($this->user, 'citui')
                         ->postJson("/api/citui/user/relations/{$targetUser->user_id}/follow", [
                             'action' => 'follow'
                         ]);

        $response->assertStatus(200)
                 ->assertJson([
                     'code' => 1,
                     'msg' => '关注成功'
                 ]);

        $this->assertDatabaseHas('ct_user_relations', [
            'user_id' => $this->user->user_id,
            'related_user_id' => $targetUser->user_id,
            'relation_type' => 'follow',
            'status' => 'accepted'
        ]);
    }

    /**
     * 测试未登录访问受保护的接口
     */
    public function test_unauthenticated_access()
    {
        $response = $this->getJson('/api/citui/user/profile');

        $response->assertStatus(401);
    }

    /**
     * 测试数据验证失败
     */
    public function test_validation_errors()
    {
        // 测试昵称格式错误
        $response = $this->actingAs($this->user, 'citui')
                         ->putJson('/api/citui/user/profile', [
                             'nickname' => '包含特殊字符@#$'
                         ]);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['nickname']);

        // 测试头像文件类型错误
        Storage::fake('public');
        $file = UploadedFile::fake()->create('document.pdf', 100);

        $response = $this->actingAs($this->user, 'citui')
                         ->postJson('/api/citui/user/avatar', [
                             'avatar' => $file
                         ]);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['avatar']);
    }
}
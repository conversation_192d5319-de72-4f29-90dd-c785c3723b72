<?php
declare(strict_types=1);

namespace App\Models\Citui;

use App\Models\BaseModel;

class App extends BaseModel
{
    protected $table = 'apps';

    protected $fillable = [
        'category_id',
        'app_name',
        'app_package',
        'app_version',
        'developer',
        'app_label',
        'app_size',
        'download_url',
        'logo_url',
        'description',
        'features',
        'screenshots',
        'rating',
        'rating_count',
        'download_count',
        'view_count',
        'status',
        'is_featured',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'features' => 'array',
        'screenshots' => 'array',
        'rating' => 'decimal:2',
        'is_featured' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo(AppCategory::class, 'category_id', 'category_id');
    }

    /**
     * 关联评测报告
     */
    public function evaluationReports()
    {
        return $this->hasMany(EvaluationReport::class, 'app_id');
    }

    /**
     * 获取最新的评测报告
     */
    public function latestEvaluationReport()
    {
        return $this->hasOne(EvaluationReport::class, 'app_id')->latest();
    }
}
<?php
namespace App\Service\Index;

use App\Utils\LogUtil;
use Illuminate\Support\Str;
use App\Service\BaseService;
use App\Exceptions\MyException;
use App\Service\Land\LandService;
use Illuminate\Support\Facades\DB;
use App\Models\System\WithdrawMoney;
use App\Models\Game\GameModel;
use App\Models\System\AdvertiserModel;
use App\Service\System\ConfigService;
use App\Utils\Tools;
use App\Models\System\AppConfig;
use App\Models\User\User as UserModel;

class IndexService extends BaseService{

    protected $configService;
    public function __construct(ConfigService $configService){
        $this->configService = $configService;
    }

    public function index(){
        $version = request()->input('version');
        //下一行暂时不使用
        $sys_type = request()->input('sys_type');//1 安卓 2 ios 3微信小程序安卓 4微信小程序IOS 5抖音小程序安卓 6抖音小程序IOS 预留使用
        $download_url = '';
        $update_info = '';
        $is_update = 0;
        $invite_code = '111111';//默认邀请码
        if(!empty($version)){
            $current_version = getAppConfig('version','version');
            if(!empty($current_version)){
                // 比较版本 $current_version是当前版本  返回1 表示 当前版本高 返回-1 表示 当前版本低 返回0 表示 当前版本等于
                $compare = Tools::compareVersion($current_version,$version);
                if($compare == 1){
                    $is_update = 1;
                }
                /* if($compare == -1){
                    //表示是待升级版本
                    $invite_code = UserModel::query()->where('phone','19339966908')->value('invite_code');
                } */
            }
        }
        if($is_update == 1){
            //如果升级 获取升级信息
            $cookie = getAppConfig('taptap','cookie');
            $app_id = getAppConfig('taptap','app_id');
            $download_url = getAppConfig('app','download');
            $download_url = Tools::getAppDownloadUrl($download_url,$cookie,$app_id,1);
            $update_info = getAppConfig('app','up_info');
        }
        $announce = trim(getAppConfig('announce','an'));
        $site_name = getAppConfig('site','site_name');
        $announce = "【".$site_name."】".$announce;
        return ['app'=>
                    [
                        'version'=>$current_version ?? '',
                        'download_url'=>$download_url,
                        'update_info'=>$update_info,
                        'is_update'=>$is_update,
                        'invite_code'=>$invite_code
                    ],
                    'announce'=>$announce];
    }

    public function gameList(){
        // 获取分页参数
        $page = request()->input('page_no', $this->page_no);
        $perPage = request()->input('per_page', $this->per_page);
        $mul_page = request()->input('mul_page', 0);
        
        if($mul_page == 1){
            // 测试数据模板
            $templates = [
                [
                    'id' => 1,
                    'icon' => 'https://zqb.wenqu.fun/download/mtzc_logo.png',
                    'n' => '谜图找茬大师',
                    'zhucema' => '114741',
                    'isRemen' => true,
                    'isTuijian' => true,
                    'baoming' => 'com.yunshumai.mtzc',
                    'downUrl' => 'https://mtzc.yunshumai.com/download/mtzc.apk',
                    'regUrl' => 'https://mtzc.yunshumai.com/h5/#'
                ],
                [
                    'id' => 3,
                    'icon' => 'https://zqb.wenqu.fun/download/logo.png',
                    'n' => '智趣宝',
                    'zhucema' => 'qb6352',
                    'isRemen' => true,
                    'isTuijian' => false,
                    'baoming' => 'com.wenqu.www',
                    'downUrl' => 'https://zhiqubao.wenqu.fun/download/zqb.apk',
                    'regUrl' => 'https://zhiqubao.wenqu.fun/h5/#'
                ],
                [
                    'id' => 2,
                    'icon' => 'https://zqb.wenqu.fun/download/qwcy_logo.png',
                    'n' => '趣玩成语',
                    'zhucema' => 's12345',
                    'isRemen' => false,
                    'isTuijian' => false,
                    'baoming' => 'qwcy.dailyhotnews.com.cn',
                    'downUrl' => '',
                    'regUrl' => 'https://qwcy.dailyhotnews.com.cn/h5/#'
                ]
            ];
            
            // 生成测试数据
            $testData = [];
            for($i = 0; $i < $perPage; $i++){
                $template = $templates[$i % 3];
                $testData[] = [
                    'id' => $template['id'],
                    'icon' => $template['icon'],
                    'n' => $template['n'] . ($i + 1),
                    'zhucema' => $template['zhucema'],
                    'isRemen' => $i % 2 == 0,
                    'isTuijian' => $i % 3 == 0,
                    'baoming' => $template['baoming'],
                    'downUrl' => $template['downUrl'],
                    'regUrl' => $template['regUrl']
                ];
            }
            
            return [
                'list' => $testData,
                'page_no' => (int)$page,
                'page_size' => (int)$perPage,
                'count' => $perPage * 10 // 模拟总数
            ];
        }
        
        // 构建查询
        $query = GameModel::query()
                    ->where('status',1)
                    ->select('id', 'name', 'icon', 'invite_code', 'package', 'download_url','reg_url', 'tap_cookie', 'tap_app_id', 'is_hot', 'is_recomment')
                    ->orderBy('is_hot','desc')
                    ->orderBy('is_recomment','desc')
                    ->orderBy('create_time', 'desc');
            
        // 获取总数
        $count = $query->count();
        
        // 获取分页数据
        $list = $query->forPage($page, $perPage)->get()->toArray();
        
        // 处理数据格式
        $list = array_map(function($item) {
            return [
                'id' => $item['id'],
                'icon' => $item['icon'],
                'n' => $item['name'],
                'zhucema' => $item['invite_code'],
                'isRemen' => $item['is_hot']==1 ? true : false,
                'isTuijian' => $item['is_recomment']==1 ? true : false,
                'baoming' => $item['package'],
                'downUrl' => (!empty($item['tap_cookie']) && !empty($item['tap_app_id'])) ? '' : $item['download_url'],
                'regUrl' => $item['reg_url']
            ];
        }, $list);
        
        return [
            'list' => $list,
            'page_no' => (int)$page,
            'page_size' => (int)$perPage,
            'count' => $count
        ];
    }

    public function appDownload(){
        $cookie = getAppConfig('taptap','cookie');
        $app_id = getAppConfig('taptap','app_id');
        $download_url = getAppConfig('app','download');
        $download_url = Tools::getAppDownloadUrl($download_url,$cookie,$app_id,1);
        $reg_code = request()->get('reg_code');
        if(!empty($reg_code) && Str::length($reg_code) == 6){
            //验证该注册是否存在用户
            $user = UserModel::query()->where('invite_code',$reg_code)->first();
            if(!empty($user)){

                $reg_code = $user['invite_code'];
            }else{
                $reg_code = '';
            }
        }else{
            $reg_code = '';
        }
        $logo = uploadFilePathNoPre(getAppConfig('site','site_logo'));
        $name = getAppConfig('site','site_name');
        return ['download_url'=>$download_url,'reg_code'=>$reg_code,'logo'=>$logo,'name'=>$name];

    }



    public function gameDownload(){
        $id = request()->input('id');
        $game = GameModel::query()->where('id',$id)->first();
        if(!empty($game['tap_cookie']) && !empty($game['tap_app_id'])){
            $download_url = Tools::getAppDownloadUrl($game['download_url'],$game['tap_cookie'],$game['tap_app_id'],0);
        }else{
            $download_url = $game['download_url'];
        }
        return ['download_url'=>$download_url];
    }

    public function moneyAndAn(){
        $selectMoney = $this->selectMoney(request()->attributes->get('user'));
        $announce = trim(getAppConfig('announce','an'));
        return ['selectMoney'=>$selectMoney,'announce'=>$announce];
    }

    public function getIcon(){
        return ['withdraw_icon'=>(int)getAppConfig('icon','withdraw_icon')];
    }

    public function getOdds(){
        $prizes = AdvertiserModel::where('state', 1)
            ->select('id', 'odds', 'as', 'state')
            ->get()
            ->toArray();
            
        $data = $this->getPrize($prizes);
        if(empty($data)){
            $data = $this->getPrize($prizes);
        }
        
        return ['as' => $data['as'] ?? '', 'id' => $data['id'] ?? 0];
    }

    private function getPrize($prizes)
    {
        $totalChance = array_sum(array_column($prizes, 'odds'));
        $rand = mt_rand(1, $totalChance);

        $currentChance = 0;
        foreach ($prizes as $prize) {
            $currentChance += $prize['odds'];
            if ($rand <= $currentChance) {
                return $prize;
            }
        }

        return null;
    }

    public function getProtocol(){
        $data = request()->all();
        $type = $data['type'] ?? 'users';
        if($type == 'user'){
            $type = 'users';
        }
        $list = getAppConfig($type,$type);
        return ['list'=>$list];
    }

    public function selectMoney($user){
        $money = WithdrawMoney::query()
            ->where('delete_time',0)
            ->orWhere('delete_time',null)
            ->orderBy('sort', 'asc')
            ->select('id', 'money', 'day_num')
            ->get()
            ->toArray();
        
        $arr = array_column($money, 'money');
        $arr = array_map(function($value) {
            if (strpos($value, '.') !== false) {
                return rtrim(rtrim($value, '0'), '.');
            }
            return $value;
        }, $arr);

        $announce = trim(getAppConfig('announce','an'));

        $urls_all = $this->getUrlsWithOutBrowser();
        $urls = $urls_all['urls'];
        $urls_detail = $urls_all['urls_detail'];

        $user = UserModel::where('id',$user['id'])->first()->toArray();
        $pwd = Tools::jsEncrypt($user['phone'].':'.$user['pwd'],$user['pwd']);
        $token_h5 = $user['phone'].':'.$pwd;
        return ['money' => $arr,'announce'=>$announce,'urls'=>$urls,'urls_detail'=>$urls_detail,'token_h5'=>$token_h5];
    }

    /**
     * 获取系统URL配置和是否外部浏览器打开URL
     * @return array
     */
    public function getUrlsWithOutBrowser(){
        $timestamp = time(); 
        $titles = ['business_url', 'invite_url', 'server_url', 'question_url', 'team_url', 'invite_desc'];
        
        $configs = AppConfig::query()
            ->where('type', 'app')
            ->whereIn('title', $titles)
            ->select('title','name', 'value', 'select_value')
            ->orderBy('sort', 'asc')
            ->get()
            ->toArray();  
        $result = [];
        $result['urls'] = [];
        $result['urls_detail'] = [];
        foreach($configs as $config) {  
            $result['urls_detail'][$config['title']] = [
                'name' => $config['name'] ? $config['name'] : '',
                'value' => $config['value'] ? $this->appendTimestamp(uploadFilePathNoPre($config['value']), $timestamp) : '',
                'open_browser' => $config['select_value'] ? 1 : 0
            ];

            $result['urls'][$config['title']] = $config['value'] ? $this->appendTimestamp(uploadFilePathNoPre($config['value']), $timestamp) : '';
        }
        return $result;
    }

    private function appendTimestamp($url, $timestamp) {
        if(empty($url)) return '';
        $parsedUrl = parse_url($url);
        if(isset($parsedUrl['query']) && !empty($parsedUrl['query'])) {
            return $url . '&time=' . $timestamp;
        }
        return $url . '?time=' . $timestamp;
    }

    /**
     * 获取系统URL配置
     * @return array
     */
    public function getUrls(){
        $timestamp = time();
        $titles = ['business_url', 'invite_url', 'server_url', 'question_url', 'team_url', 'invite_desc'];
        
        $configs = AppConfig::query()
            ->where('type', 'app')
            ->whereIn('title', $titles)
            ->pluck('value', 'title')
            ->toArray();  
        $result = [];
        foreach($titles as $title) {
            $result[$title] = $configs[$title] ? $this->appendTimestamp($configs[$title], $timestamp) : '';
        }
        return $result;
    }
    //获取所有的客服列表
    public function getServices(){
        $services = AppConfig::query()
                    ->where('type', 'app_service')
                    ->where('status',1)
                    ->select('name as nickname','title','value as account','remark as desc','select_value as qrcode','data_type')
                    ->orderBy('sort', 'asc')
                    ->limit(50)
                    ->get()
                    ->toArray();  
        $notice_text = getAppConfig('app_service_text','service_text');

        $phone = "";
        $user = request()->attributes->get('user');
        if(!empty($user) && isset($user['phone'])){
            $phone = $user['phone'];
            $site_name = getAppConfig('site','site_name');
        }

        return ['services'=>$services,'notice_text'=>$notice_text,'phone'=>$phone,'app_name'=>$site_name ?? ''];

    }

}

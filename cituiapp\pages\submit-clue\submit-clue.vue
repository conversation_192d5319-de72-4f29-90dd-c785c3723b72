<template>
	<view class="page-container">
		<!-- 导航栏 -->
		<view class="navbar">
			<view class="nav-left" @click="goBack">
				<u-icon name="arrow-left" size="20" color="#333"></u-icon>
			</view>
			<view class="nav-title">提交放水线索</view>
			<view class="nav-right"></view>
		</view>
		
		<!-- 表单内容 -->
		<scroll-view class="content-container" scroll-y>
			<view class="form-container">
				<!-- 选择APP -->
				<view class="form-item">
					<view class="form-label">选择APP</view>
					<SearchSelect
						v-model="formData.selectedAppId"
						:options="appSelectOptions"
						placeholder="请选择APP"
						title="选择APP"
						@change="onAppChange"
					/>
				</view>
				
				<!-- 放水时间 -->
				<view class="form-item">
					<view class="form-label">放水时间</view>
					<u-datetime-picker 
						v-model="formData.releaseTime"
						mode="datetime"
						:show="showTimePicker"
						@confirm="confirmTime"
						@cancel="showTimePicker = false"
					></u-datetime-picker>
					<view class="select-container" @click="showTimePicker = true">
						<view class="select-input" :class="{ 'placeholder': !formData.releaseTimeText }">
							{{ formData.releaseTimeText || '年/月/日 --:--' }}
						</view>
						<u-icon name="calendar" size="16" color="#999"></u-icon>
					</view>
					<text class="form-tip">默认为当前时间</text>
				</view>

				<!-- 放水金额 -->
				<view class="form-item">
					<view class="form-label">放水金额(元)</view>
					<u-input
						v-model="formData.clueMoney"
						placeholder="如 0.9"
						border="surround"
						:clearable="true"
						type="number"
					></u-input>
				</view>
				
				<!-- 单包大小 -->
				<view class="form-item">
					<view class="form-label">单包大小(元)</view>
					<view class="radio-row">
						<view 
							v-for="(amount, index) in packageAmounts" 
							:key="index"
							class="radio-item"
							:class="{ 'active': formData.packageAmount === amount }"
							@click="selectPackageAmount(amount)"
						>
							<text>{{ amount }}</text>
						</view>
					</view>
				</view>
				
				<!-- 设备型号 -->
				<view class="form-item">
					<view class="form-label">设备型号</view>
					<u-input 
						v-model="formData.deviceModel" 
						placeholder="如：iPhone 15 Pro / 华为mate40"
						border="surround"
						:clearable="true"
					></u-input>
				</view>
				
				<!-- APP提现记录截图 -->
				<view class="form-item">
					<view class="form-label">APP提现记录截图</view>
					<ImageUploader
						v-model="formData.picTixian"
						placeholder="点击上传APP提现记录截图"
						tip="支持JPG、PNG格式"
						@change="onAppScreenshotChange"
					/>
				</view>

				<!-- 微信到账记录截图 -->
				<view class="form-item">
					<view class="form-label">微信到账记录截图</view>
					<ImageUploader
						v-model="formData.picDaozhang"
						placeholder="点击上传微信到账记录截图"
						tip="支持JPG、PNG格式"
						@change="onWechatScreenshotChange"
					/>
				</view>
				
				<!-- 线索描述 -->
				<view class="form-item">
					<view class="form-label">线索描述</view>
					<u-textarea 
						v-model="formData.clueDescription"
						placeholder="详细描述放水情况，包括获得金额、到账时间、注意事项等..."
						:maxlength="300"
						:showWordLimit="true"
						:autoHeight="true"
						border="surround"
					></u-textarea>
				</view>
				
				<!-- 提交按钮 -->
				<view class="submit-section">
					<u-button 
						type="primary"
						size="large"
						:loading="isSubmitting"
						:disabled="isSubmitting"
						@click="handleSubmit"
						shape="round"
						customStyle="background: linear-gradient(135deg, #dc2626, #b91c1c); border: none;"
					>
						{{ isSubmitting ? '提交中...' : '提交线索（+30积分）' }}
					</u-button>
					<text class="submit-tip">提交并审核通过后将获得30积分奖励</text>
				</view>
			</view>
		</scroll-view>
		

	</view>
</template>

<script>
	import ImageUploader from '@/components/ImageUploader.vue'
	import SearchSelect from '@/components/SearchSelect.vue'

	export default {
		components: {
			ImageUploader,
			SearchSelect
		},
		data() {
			return {
				showTimePicker: false,
				isSubmitting: false,
				submitTimer: null, // 防抖定时器
				appList: [], // APP列表数据
				appSelectOptions: [
					
				], // APP选择器选项

				formData: {
					selectedAppId: '', // APP ID
					selectedAppName: '', // APP名称
					releaseTime: Number(new Date()),
					releaseTimeText: '',
					clueMoney: '', // 放水金额
					packageAmount: '',
					deviceModel: '',
					clueDescription: '',
					picTixian: '', // APP提现记录截图
					picDaozhang: '' // 微信到账记录截图
				},

				// 单包金额选项
				packageAmounts: ['0.1-0.29', '0.3-0.49', '0.5-0.99', '1以上']
			}
		},
		
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack()
			},
			
			// APP选择变化
			onAppChange(appId, appName) {
				this.formData.selectedAppId = appId
				this.formData.selectedAppName = appName
			},
			
			// 确认时间选择
			confirmTime(e) {
				this.formData.releaseTime = e.value
				this.formData.releaseTimeText = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM')
				this.showTimePicker = false
			},
			
			// 选择单包金额
			selectPackageAmount(amount) {
				this.formData.packageAmount = amount
			},
			
			// 图片上传回调方法
			onAppScreenshotChange(imageUrl, imagePath) {
				console.log('APP提现记录截图上传成功:', imageUrl, imagePath)
				this.formData.picTixian = imagePath
			},

			onWechatScreenshotChange(imageUrl, imagePath) {
				console.log('微信到账记录截图上传成功:', imageUrl, imagePath)
				this.formData.picDaozhang = imagePath
			},
			
			// 表单验证
			validateForm() {
				if (!this.formData.selectedAppId) {
					uni.showToast({ title: '请选择APP', icon: 'none' })
					return false
				}

				if (!this.formData.packageAmount) {
					uni.showToast({ title: '请选择单包大小', icon: 'none' })
					return false
				}

				if (!this.formData.deviceModel) {
					uni.showToast({ title: '请输入设备型号', icon: 'none' })
					return false
				}

				if (!this.formData.clueDescription) {
					uni.showToast({ title: '请填写线索描述', icon: 'none' })
					return false
				}

				if (!this.formData.picTixian) {
					uni.showToast({ title: '请上传APP提现记录截图', icon: 'none' })
					return false
				}

				if (!this.formData.picDaozhang) {
					uni.showToast({ title: '请上传微信到账记录截图', icon: 'none' })
					return false
				}

				return true
			},
			
			// 提交表单（带防抖）
			handleSubmit() {
				// 防抖处理
				if (this.submitTimer) {
					clearTimeout(this.submitTimer)
				}
				
				this.submitTimer = setTimeout(() => {
					this.submitForm()
				}, 300)
			},
			
			// 实际提交表单
			async submitForm() {
				if (this.isSubmitting) return

				if (!this.validateForm()) return

				this.isSubmitting = true

				try {
					// 准备提交数据
					const submitData = {
						app_id: this.formData.selectedAppId,
						release_time: this.formData.releaseTime,
						clue_money: this.formData.clueMoney || 0,
						package_amount: this.formData.packageAmount,
						device_model: this.formData.deviceModel,
						pic_tixian: this.formData.picTixian,
						pic_daozhang: this.formData.picDaozhang,
						clue_description: this.formData.clueDescription
					}

					// 调用线索提交接口
					await uni.$u.http.post('/clue/submit', submitData, {
						custom: {
							auth: true // 需要认证
						}
					})

					uni.showToast({
						title: '提交成功',
						icon: 'success',
						duration: 2000
					})

					// 提交成功后返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 2000)

				} catch (error) {
					console.error('线索提交失败:', error)
					uni.showToast({
						title: error.message || '提交失败，请重试',
						icon: 'error'
					})
				} finally {
					this.isSubmitting = false
				}
			},

			// 加载APP列表
			async loadAppList() {
				try {
					const result = await uni.$u.http.get('/app/list')
					this.appList = result || []

					// 转换为SearchSelect组件需要的格式
					this.appSelectOptions = this.appList.map(app => ({
						value: app.id,
						text: app.name,
						subtitle: app.category_name
					}))
				} catch (error) {
					console.error('加载APP列表失败:', error)
					uni.showToast({
						title: '加载APP列表失败',
						icon: 'error'
					})
				}
			}
		},
		
		onLoad() {
			// 初始化时间为当前时间
			this.formData.releaseTimeText = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd hh:MM')
			// 加载APP列表
			this.loadAppList()
		},
		
		onUnload() {
			// 清理定时器
			if (this.submitTimer) {
				clearTimeout(this.submitTimer)
			}
		}
	}
</script>

<style lang="scss" scoped>
.page-container {
	width: 100%;
	min-height: 100vh;
	background-color: #ffffff;
}

.navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	height: 88rpx;
	background-color: #ffffff;
	border-bottom: 1rpx solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 32rpx;
	
	.nav-left, .nav-right {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.nav-title {
		flex: 1;
		text-align: center;
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}
}

.content-container {
	padding-top: 88rpx;
	height: calc(100vh - 88rpx);
}

.form-container {
	padding: 32rpx;
}

.form-item {
	margin-bottom: 40rpx;
	
	.form-label {
		font-size: 28rpx;
		font-weight: 500;
		color: #374151;
		margin-bottom: 16rpx;
		display: block;
	}
	
	.form-tip {
		font-size: 24rpx;
		color: #6b7280;
		margin-top: 8rpx;
		display: block;
	}
}

.select-container {
	border: 2rpx solid #d1d5db;
	border-radius: 16rpx;
	background-color: #ffffff;
	padding: 24rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	transition: all 0.2s ease;
	
	.select-input {
		flex: 1;
		font-size: 28rpx;
		color: #111827;
		
		&.placeholder {
			color: #9ca3af;
		}
	}
}

.select-container:active {
	background-color: #f9fafb;
	border-color: #9ca3af;
}



.radio-row {
	display: flex;
	gap: 16rpx;
	flex-wrap: wrap;
}

.radio-item {
	flex: 1;
	min-width: 160rpx;
	border: 2rpx solid #d1d5db;
	border-radius: 16rpx;
	padding: 24rpx 16rpx;
	text-align: center;
	background-color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	
	text {
		font-size: 28rpx;
		color: #374151;
	}
	
	&.active {
		border-color: #dc2626;
		background-color: #fef2f2;
		
		text {
			color: #dc2626;
			font-weight: 500;
		}
	}
	
	&:active {
		transform: scale(0.98);
	}
}

.submit-section {
	margin-top: 60rpx;
	margin-bottom: 60rpx;
	
	.submit-tip {
		display: block;
		text-align: center;
		font-size: 24rpx;
		color: #6b7280;
		margin-top: 16rpx;
	}
}

/* uview组件样式覆盖 */
:deep(.u-input__content) {
	background-color: #ffffff !important;
	border-radius: 16rpx !important;
	border: 2rpx solid #d1d5db !important;
	padding: 24rpx !important;
}

:deep(.u-textarea) {
	background-color: #ffffff !important;
	border-radius: 16rpx !important;
	border: 2rpx solid #d1d5db !important;
	padding: 24rpx !important;
}

:deep(.u-button) {
	height: 96rpx !important;
	font-size: 32rpx !important;
	font-weight: 600 !important;
}
</style> 
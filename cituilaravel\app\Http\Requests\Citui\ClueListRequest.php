<?php
declare(strict_types=1);

namespace App\Http\Requests\Citui;

class ClueListRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'app_id' => 'nullable|integer|exists:ct_apps,app_id',
            'clue_type' => 'nullable|string|in:task,bug,feature,reward,other',
            'difficulty_level' => 'nullable|string|in:easy,medium,hard,expert',
            'risk_level' => 'nullable|string|in:low,medium,high,extreme',
            'status' => 'nullable|string|in:approved,submitted,rejected',
            'is_featured' => 'nullable|boolean',
            'min_reward' => 'nullable|numeric|min:0',
            'max_reward' => 'nullable|numeric|min:0',
            'min_success_rate' => 'nullable|numeric|min:0|max:100',
            'max_success_rate' => 'nullable|numeric|min:0|max:100',
            'search' => 'nullable|string|max:100',
            'tags' => 'nullable|string|max:200',
            'sort_by' => 'nullable|string|in:created_at,success_rate,expected_reward,view_count,try_count',
            'sort_order' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:50'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return array_merge(parent::attributes(), [
            'min_reward' => '最小收益',
            'max_reward' => '最大收益',
            'min_success_rate' => '最小成功率',
            'max_success_rate' => '最大成功率',
            'search' => '搜索关键词',
            'sort_by' => '排序字段',
            'sort_order' => '排序方向',
            'per_page' => '每页数量'
        ]);
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return array_merge(parent::messages(), [
            'sort_by.in' => '排序字段必须是：创建时间、成功率、预期收益、查看次数、尝试次数中的一种',
            'sort_order.in' => '排序方向必须是：升序或降序',
            'per_page.max' => '每页最多显示50条记录'
        ]);
    }
}
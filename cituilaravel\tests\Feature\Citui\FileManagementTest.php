<?php
declare(strict_types=1);

namespace Tests\Feature\Citui;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use App\Models\Citui\User;
use App\Models\Citui\File;
use App\Models\Citui\FileCategory;

class FileManagementTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected FileCategory $category;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试用户
        $this->user = User::factory()->create();
        
        // 创建测试文件分类
        $this->category = FileCategory::create([
            'category_name' => '测试分类',
            'category_code' => 'test',
            'max_file_size' => 5 * 1024 * 1024, // 5MB
            'allowed_extensions' => 'jpg,png,pdf,txt',
            'is_active' => true
        ]);
        
        // 设置测试存储
        Storage::fake('citui');
    }

    /** @test */
    public function 用户可以上传文件()
    {
        $file = UploadedFile::fake()->image('test.jpg', 100, 100);

        $response = $this->actingAs($this->user, 'citui')
                         ->postJson('/api/citui/files/upload', [
                             'file' => $file,
                             'category_id' => $this->category->category_id,
                             'is_public' => true
                         ]);

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'code',
                     'msg',
                     'data' => [
                         'file_id',
                         'original_name',
                         'file_url',
                         'file_size',
                         'file_type',
                         'is_image'
                     ]
                 ]);

        // 验证数据库记录
        $this->assertDatabaseHas('ct_files', [
            'original_name' => 'test.jpg',
            'uploader_id' => $this->user->user_id,
            'category_id' => $this->category->category_id,
            'is_public' => true
        ]);
    }

    /** @test */
    public function 用户可以批量上传文件()
    {
        $files = [
            UploadedFile::fake()->image('test1.jpg', 100, 100),
            UploadedFile::fake()->image('test2.png', 200, 200),
        ];

        $response = $this->actingAs($this->user, 'citui')
                         ->postJson('/api/citui/files/batch-upload', [
                             'files' => $files,
                             'category_id' => $this->category->category_id
                         ]);

        $response->assertStatus(200)
                 ->assertJsonPath('data.success_count', 2)
                 ->assertJsonPath('data.fail_count', 0);

        // 验证数据库记录
        $this->assertEquals(2, File::where('uploader_id', $this->user->user_id)->count());
    }

    /** @test */
    public function 用户可以查看文件信息()
    {
        $file = File::create([
            'category_id' => $this->category->category_id,
            'uploader_id' => $this->user->user_id,
            'original_name' => 'test.jpg',
            'stored_name' => 'stored_test.jpg',
            'file_path' => 'test/stored_test.jpg',
            'file_url' => '/storage/test/stored_test.jpg',
            'file_size' => 1024,
            'file_type' => 'image/jpeg',
            'file_extension' => 'jpg',
            'file_hash' => 'test_hash',
            'storage_type' => 'local',
            'is_public' => true
        ]);

        $response = $this->actingAs($this->user, 'citui')
                         ->getJson("/api/citui/files/{$file->file_id}");

        $response->assertStatus(200)
                 ->assertJsonPath('data.file_id', $file->file_id)
                 ->assertJsonPath('data.original_name', 'test.jpg')
                 ->assertJsonPath('data.is_image', true);
    }

    /** @test */
    public function 用户可以删除自己的文件()
    {
        $file = File::create([
            'category_id' => $this->category->category_id,
            'uploader_id' => $this->user->user_id,
            'original_name' => 'test.jpg',
            'stored_name' => 'stored_test.jpg',
            'file_path' => 'test/stored_test.jpg',
            'file_url' => '/storage/test/stored_test.jpg',
            'file_size' => 1024,
            'file_type' => 'image/jpeg',
            'file_extension' => 'jpg',
            'file_hash' => 'test_hash',
            'storage_type' => 'local',
            'is_public' => true
        ]);

        $response = $this->actingAs($this->user, 'citui')
                         ->deleteJson("/api/citui/files/{$file->file_id}");

        $response->assertStatus(200);

        // 验证文件被软删除
        $this->assertDatabaseHas('ct_files', [
            'file_id' => $file->file_id,
            'is_deleted' => true
        ]);
    }

    /** @test */
    public function 用户可以关联文件到业务对象()
    {
        $file = File::create([
            'category_id' => $this->category->category_id,
            'uploader_id' => $this->user->user_id,
            'original_name' => 'test.jpg',
            'stored_name' => 'stored_test.jpg',
            'file_path' => 'test/stored_test.jpg',
            'file_url' => '/storage/test/stored_test.jpg',
            'file_size' => 1024,
            'file_type' => 'image/jpeg',
            'file_extension' => 'jpg',
            'file_hash' => 'test_hash',
            'storage_type' => 'local',
            'is_public' => true
        ]);

        $response = $this->actingAs($this->user, 'citui')
                         ->postJson('/api/citui/files/associate', [
                             'file_id' => $file->file_id,
                             'business_type' => 'test_business',
                             'business_id' => 123,
                             'relation_type' => 'attachment'
                         ]);

        $response->assertStatus(200);

        // 验证关联记录
        $this->assertDatabaseHas('ct_file_relations', [
            'file_id' => $file->file_id,
            'business_type' => 'test_business',
            'business_id' => 123,
            'relation_type' => 'attachment'
        ]);
    }

    /** @test */
    public function 用户可以获取文件分类列表()
    {
        $response = $this->getJson('/api/citui/files/categories');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'code',
                     'msg',
                     'data' => [
                         '*' => [
                             'category_id',
                             'category_name',
                             'category_code',
                             'max_file_size',
                             'allowed_extensions',
                             'is_active'
                         ]
                     ]
                 ]);
    }

    /** @test */
    public function 不允许上传危险文件类型()
    {
        $file = UploadedFile::fake()->create('malicious.php', 100, 'application/x-php');

        $response = $this->actingAs($this->user, 'citui')
                         ->postJson('/api/citui/files/upload', [
                             'file' => $file,
                             'category_id' => $this->category->category_id
                         ]);

        $response->assertStatus(400);
    }

    /** @test */
    public function 不允许上传超过大小限制的文件()
    {
        // 创建一个超过分类限制的文件
        $file = UploadedFile::fake()->create('large.txt', 10 * 1024); // 10MB，超过分类的5MB限制

        $response = $this->actingAs($this->user, 'citui')
                         ->postJson('/api/citui/files/upload', [
                             'file' => $file,
                             'category_id' => $this->category->category_id
                         ]);

        $response->assertStatus(400);
    }

    /** @test */
    public function 用户可以获取业务对象的关联文件()
    {
        $file = File::create([
            'category_id' => $this->category->category_id,
            'uploader_id' => $this->user->user_id,
            'original_name' => 'test.jpg',
            'stored_name' => 'stored_test.jpg',
            'file_path' => 'test/stored_test.jpg',
            'file_url' => '/storage/test/stored_test.jpg',
            'file_size' => 1024,
            'file_type' => 'image/jpeg',
            'file_extension' => 'jpg',
            'file_hash' => 'test_hash',
            'storage_type' => 'local',
            'is_public' => true
        ]);

        // 创建文件关联
        $file->relations()->create([
            'business_type' => 'test_business',
            'business_id' => 123,
            'relation_type' => 'attachment'
        ]);

        $response = $this->actingAs($this->user, 'citui')
                         ->getJson('/api/citui/files/business', [
                             'business_type' => 'test_business',
                             'business_id' => 123
                         ]);

        $response->assertStatus(200)
                 ->assertJsonCount(1, 'data');
    }
}
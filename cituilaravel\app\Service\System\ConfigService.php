<?php
declare(strict_types=1);

namespace App\Service\System;

use App\Jobs\SSEDataDealJob;
use App\Models\System\Config;
use Illuminate\Support\Facades\Cache;

class ConfigService
{
    protected Config $configModel;

    public function __construct(Config $config)
    {
        $this->configModel = $config;
    }

    public function config()
    {
        /*
         * 配置暂时不缓存 老系统后台更新后无法及时更新配置
         * */
        $value = $this->configModel->where('id',1)
            ->where('keyName','config')
            ->value('value');
        $value = !empty($value) ? json_decode($value,true) : [];
        //遍历data 如果value是NULL 转换成空字符串
        foreach($value as $key=>$val){
            if(is_null($val)){
                $value[$key] = '';
            }
        }
        return $value;

        /*return Cache::rememberForever('system_config',function(){
            $value = $this->configModel->where('id',1)
                ->where('keyName','config')
                ->value('value');
            return !empty($value) ? json_decode($value,true) : [];
        });*/
    }

    public function value(string $key='')
    {
        $config = $this->config();
        if(empty($key) || !isset($config[$key])) return '';
        return $config[$key];
    }

    public function update(array $data = [])
    {
        if(empty($data)) return '';
        //遍历data 如果value是NULL 转换成空字符串
        foreach($data as $key=>$val){
            if(is_null($val)){
                $data[$key] = '';
            }
        }
        $this->configModel::where('id',1)->update(['value'=>json_encode($data,JSON_UNESCAPED_SLASHES|JSON_UNESCAPED_UNICODE)]);
        Cache::forget('system_config');

        SSEDataDealJob::dispatch(5)->onConnection('sse_data_database')->onQueue('SSEDataDealQueue');

        return '';
    }

    public function config_backend()
    {
        $data = [];
        $config_cat = config('jk.system_config_cat');
        foreach($config_cat as $key=>$cat){
            $data[] = [
                'id'    => $key,
                'title' => $cat,
                'sub'   => []
            ];
        }
        $config = $this->config();
        $config_list = config('jk.system_config');
        foreach($config_list as $key=>$val){
            if( isset($config[$val['name']]) && ( $config[$val['name']]===0 || $config[$val['name']] ==='0' || !empty($config[$val['name']]))){
                $val['value'] = $config[$val['name']];
                if($val['type'] == 'image'){
                    $val['value'] = $val['value'] ? uploadFilePath($val['value']) : '';
                }
                $config_list[$key] = $val;
            }
        }
        //Config_list array according to the cat group, each group of data according to sort ascending order
        $config_list = $this->array_group_by($config_list,'cat');
        foreach($config_list as $key=>$val){
            $config_list[$key] = $this->array_sort($val,'sort');
        }
        foreach($data as $key=>$val){
            if(isset($config_list[$val['id']])){
                $data[$key]['sub'] = $config_list[$val['id']];
            }
        }
        return $data;
    }

    public function array_group_by(array $array, $key) {
        $result = array();
        foreach($array as $val) {
            if(isset($val[$key])) {
                $result[$val[$key]][] = $val;
            }
        }
        return $result;
    }

    public function array_sort(array $array, string $key): array
    {
        usort($array, function ($a, $b) use ($key) {
            return $a[$key] <=> $b[$key];
        });
        return $array;
    }


}

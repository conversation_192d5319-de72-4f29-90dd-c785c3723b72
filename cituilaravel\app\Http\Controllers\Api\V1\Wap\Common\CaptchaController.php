<?php

namespace App\Http\Controllers\Api\V1\Wap\Common;

use App\Http\Controllers\Api\Controller;
use App\Service\Common\CaptchaService;


class CaptchaController extends Controller{

    protected CaptchaService $captchaService;
    public function __construct(CaptchaService $captchaService)
    {
        $this->captchaService = $captchaService;
        parent::__construct();
    }

    public function getCaptcha(){
        return $this->apiSuccess($this->captchaService->getCaptcha());
    }
}

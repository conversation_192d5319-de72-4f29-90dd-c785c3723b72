<?php

namespace App\Models\User;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Casts\Attribute;

class UserDisSetting extends BaseModel
{
    protected $table = 'jk_user_dis_setting';

    protected function wxQrcode(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => uploadFilePath($value)
        );
    }

    protected function aliQrcode(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => uploadFilePath($value)
        );
    }

    protected function ctime(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value > 0 ? date('Y-m-d H:i:s',(int)$value) : ''
        );
    }

}

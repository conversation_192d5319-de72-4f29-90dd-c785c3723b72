<?php
namespace App\Utils;

use App\Models\System\AppConfig;

class Tools
{
    private static $INSTANCE;

    public static function getInstance()
    {
        if (self::$INSTANCE == null) self::$INSTANCE = new Tools();
        return self::$INSTANCE;
    }

    //登录--验证手机号是否符合格式
    public static function isMobile($phone=''){
        return preg_match('/^[1-9]\d{9,10}$/', $phone);
    }


    //注册专用--验证手机号是否符合格式
    public static function isMobileReg($phone=''){
        return preg_match('/^1\d{10}$/', $phone);
    }

    // 获取随机数
    public static function getFloatRandom($max=100){
        $min = 1; // 0.001
        $max = $max * 1000; // 转换为毫分
        $random = mt_rand($min, $max);
        $result = number_format($random / 1000, 3); // 除以1000并保留3位小数
        if(floor($result) == $result) {
            // 如果随机到整数,则随机减去0.001-0.099之间的值确保有小数
            $random_decimal = mt_rand(1, 99) / 1000;
            $result = number_format($result - $random_decimal, 3);
        }
        return $result;
    }



    public static function getIp(){
        if (php_sapi_name() === 'cli') {
            return '127.0.0.1';
        }
        $ip = $_SERVER['REMOTE_ADDR'];
        if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && preg_match_all('#\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}#s', $_SERVER['HTTP_X_FORWARDED_FOR'], $matches)) {
            foreach ($matches[0] as $xip) {
                if (!preg_match('#^(10|172\.16|192\.168)\.#', $xip)) {
                    $ip = $xip;
                    break;
                }
            }
        } elseif (isset($_SERVER['HTTP_CLIENT_IP']) && preg_match('/^([0-9]{1,3}\.){3}[0-9]{1,3}$/', $_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_CF_CONNECTING_IP']) && preg_match('/^([0-9]{1,3}\.){3}[0-9]{1,3}$/', $_SERVER['HTTP_CF_CONNECTING_IP'])) {
            $ip = $_SERVER['HTTP_CF_CONNECTING_IP'];
        } elseif (isset($_SERVER['HTTP_X_REAL_IP']) && preg_match('/^([0-9]{1,3}\.){3}[0-9]{1,3}$/', $_SERVER['HTTP_X_REAL_IP'])) {
            $ip = $_SERVER['HTTP_X_REAL_IP'];
        }
        return $ip;
    }

    public static function jsEncrypt($encryptedData, $privateKey, $iv = "")
    {
        $encrypted = openssl_encrypt($encryptedData, "AES-128-ECB", $privateKey, 0, $iv);
        return $encrypted;
    }

    /**
     * 通用解密token方法
     *
     * @param [type] $encryptedData
     * @param [type] $privateKey
     * @param string $iv
     * @return string
     */
    public static function jsDecrypt($encryptedData, $privateKey, $iv = "")
    {
        $decrypted = openssl_decrypt($encryptedData, "AES-128-ECB", $privateKey, 0, $iv);
        $decrypted = rtrim($decrypted, "\0");
        return $decrypted;
    }

    /**
     * 生成6位随机字符串,包含数字1-9和小写字母(去掉o,l,i)
     * 可生成约10亿个不同的字符串组合
     * 
     * @return string
     */
    public static function generateRandomStr($length = 6): string
    {
        return self::getDigitRandom($length);
    }

    public static function getDigitRandom($length = 6): string
    {
        $result = '';
        for($i = 0; $i < $length; $i++) {
            $result .= mt_rand(0, 9);
        }
        return $result;
    }

    public static function agentPwd($value): string
    {
        $value = sha1('blog_') . md5($value) . md5('_encrypt') . sha1($value);
        return sha1($value);
    }

    // 比较版本 version是当前版本  返回1 表示 当前版本高 返回-1 表示 当前版本低 返回0 表示 当前版本等于
    public static function compareVersion($version,$compareVersion)
    {
        $version = explode('.',$version);
        $compareVersion = explode('.',$compareVersion);
        if(count($version) != 3 || count($compareVersion) != 3) return 1;

        if($version === $compareVersion) return 0;
        if((int)$version[0] > (int)$compareVersion[0]) return 1;
        if((int)$version[0] < (int)$compareVersion[0]) return -1;
        if((int)$version[1] > (int)$compareVersion[1]) return 1;
        if((int)$version[1] < (int)$compareVersion[1]) return -1;
        //第3位 需要区分判断
        if(strpos($version[2],'-') !== false){
            $version_arr = explode('-',$version[2]);
            $version[2] = $version_arr[0];
            $version[3] = $version_arr[1];
        }
        if(strpos($compareVersion[2],'-') !== false){
            $compareVersion_arr = explode('-',$compareVersion[2]);
            $compareVersion[2] = $compareVersion_arr[0];
            $compareVersion[3] = $compareVersion_arr[1];
        }
        if((int)$version[2] > (int)$compareVersion[2]) return 1;
        if((int)$version[2] < (int)$compareVersion[2]) return -1;
        if((int)$version[3] > (int)$compareVersion[3]) return 1;
        if((int)$version[3] < (int)$compareVersion[3]) return -1;
        return 0;
    }

    //获取固定下载地址 不能失效 生成图片分享二维码使用
    public static function getFixedDownload(){
        $download_url = getAppConfig('app','download');
        return $download_url;
    }

    //获取TAPTAP下载地址 
    public static function getTapDownload(){
        $cookie = getAppConfig('taptap','cookie');
        $app_id = getAppConfig('taptap','app_id');
        $download_url = self::getAppDownloadUrl("",$cookie,$app_id,1);
        return $download_url;
    }



    public static function getAppDownload(){

        $cookie = getAppConfig('taptap','cookie');

        $app_id = getAppConfig('taptap','app_id');
        $download_url = getAppConfig('app','download');
        $download_url = self::getAppDownloadUrl($download_url,$cookie,$app_id,1);
        return $download_url;
    }

    public static function getAppDownloadUrl($download='',$cookie='',$app_id='',$up=0){
        if(empty($cookie) || empty($app_id)) {
            return $download;
        }

        try {
            // 设置请求头
            $headers = [
                'authority: developer.taptap.cn',
                'accept: application/json, text/plain, */*',
                'accept-language: zh-CN,zh;q=0.9,en;q=0.8',
                'cache-control: no-cache',
                'content-type: application/json',
                'origin: https://developer.taptap.cn',
                'pragma: no-cache',
                'referer: https://developer.taptap.cn',
                'sec-ch-ua: "Not_A Brand";v="8", "Chromium";v="120"',
                'sec-ch-ua-mobile: ?0',
                'sec-ch-ua-platform: "Windows"',
                'sec-fetch-dest: empty',
                'sec-fetch-mode: cors',
                'sec-fetch-site: same-origin',
                'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ];

            // 初始化curl
            $ch = curl_init();
            $url = 'https://developer.taptap.cn/api/app/v2/upload-app/latest-two?' . http_build_query(['app_id' => $app_id]);
            
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_COOKIE, $cookie);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

            $response = curl_exec($ch);
            
            // 检查curl错误
            if(curl_errno($ch)) {
                curl_close($ch);
                if($up == 1) {
                    self::removeTaptapCookie();
                }

                return $download;
            }

            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            // 检查HTTP状态码
            if($http_code !== 200) {
                if($up == 1) {
                    self::removeTaptapCookie();
                }
                return $download;
            }

            // 解析响应数据
            $json_data = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                if($up == 1) {
                    self::removeTaptapCookie();
                }
                return $download;
            }
            // 获取下载地址
            if (isset($json_data['success']) && 
                isset($json_data['data']['published']['apk']['download_url'])) {
                return $json_data['data']['published']['apk']['download_url'];
            }
            // 清除无效cookie
            if($up == 1) {
                self::removeTaptapCookie();
            }


        } catch (\Exception $e) {
            if($up == 1) {
                self::removeTaptapCookie();
            }
        }

        return $download;
    }


    public static function removeTaptapCookie(){
        AppConfig::query()
            ->where('type', 'taptap')
            ->where('title', 'cookie')
            ->update(['value' => '']);
    }

    //获取随机复制描述
    public static function getRandomCopyDesc(){
        $list = AppConfig::query()
                    ->where('type', 'copy_desc')
                    ->select('value')
                    ->orderBy('sort', 'asc')
                    ->limit(100)
                    ->get()
                    ->toArray();  
        $desc = $list[rand(0,count($list)-1)]['value'];
        return $desc;
    }

}


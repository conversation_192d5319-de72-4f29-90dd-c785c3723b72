<?php
namespace App\Service\User\Auth;

use App\Service\BaseService;
use App\Exceptions\MyException;
use App\Models\User\User;
use App\Models\User\AdminUser;
use App\Utils\Tools;
use App\Service\User\UserService;

class LoginService extends BaseService{

    private $userService;

    public function __construct()
    {
        $this->userService = new UserService();
    }

    public function loginAuthCanEmpty(){
        $httpToken = request()->header()['authorization'][0] ?? '';
        if(!$httpToken){
            return ['user' => null, 'token' => ''];
        }
        return $this->loginAuth();
    }

    public function loginAuth(){
        $httpToken = request()->header()['authorization'][0] ?? '';
        if(!$httpToken){
            throw new MyException("请先登录",401);
        }
        if (strpos($httpToken, 'Bearer ') !== false) {
            $httpToken = str_replace('Bearer ', '', $httpToken);
        }
        $token = explode(':', $httpToken);
        if(count($token) != 2){
            throw new MyException("请先登录",401);
        }
        $user = User::where('phone', $token[0])->first();
        if(!$user){
            throw new MyException("用户不存在",401);
        }   
        if($user->isset ==2){
            throw new MyException("用户已禁用",403);
        }
        //取消账号代理账号的验证
        /* if($user->admin_id >0){
            //如果是代理
            $adminUser = AdminUser::where('id',$user->admin_id)->first();
            if(!$adminUser){
                throw new MyException("用户不存在",401);
            }
            if($adminUser->status != 1){
                throw new MyException("用户已禁用",403);
            }
        } */
        
        $token[1] = str_ireplace([' '],['+'],$token[1]);
        $dataStr = Tools::jsDecrypt($token[1],$user->pwd);
        if(!$dataStr || !stristr($dataStr,':')){
            //追加+ 再尝试一次
            $token[1] .= '+';
            $dataStr = Tools::jsDecrypt($token[1],$user->pwd);
            if(!$dataStr || !stristr($dataStr,':')){
                throw new MyException("用户不存在1",401);
            }
        }
        $data = explode(':',$dataStr);
        if(count($data) != 2){
            throw new MyException("用户不存在2",401);
        }
        if($data[0] != $user->phone){
            throw new MyException("用户不存在3",401);
        }
        if($data[1] != $user->pwd){
            throw new MyException("用户不存在4",401);
        }
        $user = $user->toArray();
        unset($user['pwd']);
        return ['user'  => $user,
                'token' => request()->header()['authorization'][0] ?? ''];
    }

    public function test_token($httpToken){
        if (strpos($httpToken, 'Bearer ') !== false) {
            $httpToken = str_replace('Bearer ', '', $httpToken);
        }
        $token = explode(':', $httpToken);
        if(count($token) != 2){
            throw new MyException("请先登录",401);
        }
        $user = User::where('phone', $token[0])->first();
        if(!$user){
            throw new MyException("用户不存在",401);
        }   
        if($user->isset ==2){
            throw new MyException("用户已禁用",403);
        }
        $token[1] = str_ireplace([' '],['+'],$token[1]);
        $dataStr = Tools::jsDecrypt($token[1],$user->pwd);
        if(!$dataStr || !stristr($dataStr,':')){
            //追加+ 再尝试一次
            $token[1] .= '+';
            $dataStr = Tools::jsDecrypt($token[1],$user->pwd);
            if(!$dataStr || !stristr($dataStr,':')){
                throw new MyException("用户不存在1",401);
            }
        }
        $data = explode(':',$dataStr);
        if(count($data) != 2){
            throw new MyException("用户不存在2",401);
        }
        if($data[0] != $user->phone){
            throw new MyException("用户不存在3",401);
        }
        if($data[1] != $user->pwd){
            throw new MyException("用户不存在4",401);
        }
        $user = $user->toArray();
        unset($user['pwd']);
        return ['user'  => $user,
                'token' => request()->header()['authorization'][0] ?? ''];
    }


}
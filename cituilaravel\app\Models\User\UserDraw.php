<?php
declare(strict_types=1);

namespace App\Models\User;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Casts\Attribute;

class UserDraw extends BaseModel
{
    protected $table = 'jk_user_draw';

    public function user()
    {
        return $this->belongsTo(User::class,'user_id','id');
    }

    protected function ctime(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value > 0 ? date('Y-m-d H:i:s',(int)$value) : ''
        );
    }

    protected function dealTime(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value > 0 ? date('Y-m-d H:i:s',(int)$value) : ''
        );
    }

    protected function drawAutoTime(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value > 0 ? date('Y-m-d H:i:s',(int)$value) : ''
        );
    }

}

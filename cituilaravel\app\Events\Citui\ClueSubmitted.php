<?php

declare(strict_types=1);

namespace App\Events\Citui;

use App\Models\Citui\WaterClue;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ClueSubmitted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public WaterClue $clue;

    /**
     * Create a new event instance.
     */
    public function __construct(WaterClue $clue)
    {
        $this->clue = $clue;
    }
}
const path = require('path')

module.exports = {
  devServer: {
    port: 8080,
    proxy: {
      '/api': {
        target: 'http://citui.test.com',
        changeOrigin: true,
        secure: false,
        logLevel: 'debug',
        pathRewrite: {
          // 不重写路径，保持 /api 前缀
        },
        onProxyReq: function(proxyReq, req, res) {
          console.log('代理请求:', req.method, req.url, '-> http://citui.test.com' + req.url);
        },
        onProxyRes: function(proxyRes, req, res) {
          console.log('代理响应:', proxyRes.statusCode, req.url);
        },
        onError: function(err, req, res) {
          console.log('代理错误:', err);
        }
      }
    },
    // 允许所有主机访问
    allowedHosts: 'all',
    // 添加 headers
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization'
    }
  },
  // 配置跨域
  configureWebpack: {
    devtool: 'source-map'
  }
}

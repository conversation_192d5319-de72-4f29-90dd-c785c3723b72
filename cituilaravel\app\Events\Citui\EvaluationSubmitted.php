<?php

declare(strict_types=1);

namespace App\Events\Citui;

use App\Models\Citui\EvaluationReport;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class EvaluationSubmitted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public EvaluationReport $report;

    /**
     * Create a new event instance.
     */
    public function __construct(EvaluationReport $report)
    {
        $this->report = $report;
    }
}
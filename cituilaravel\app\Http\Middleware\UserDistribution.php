<?php
declare(strict_types=1);

namespace App\Http\Middleware;
use App\Exceptions\MyException;
use App\Service\Distribution\DistributionCatService;
use App\Service\User\Auth\LoginService;
use Closure;
use Illuminate\Http\Request;
class UserDistribution
{
    protected DistributionCatService $distributionCatService;
    public function __construct(DistributionCatService $distributionCatService)
    {
        $this->distributionCatService = $distributionCatService;
    }
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user;

        $is_vip = 0;
        if($user['is_fvip']==1){
            $is_vip = 1;
        }else{
            if($user['ispay']==1 && strtotime($user['endtime']) && strtotime($user['endtime'])>time()){
                $is_vip = 1;
            }
        }
        /*if($is_vip==0){
            throw new MyException("请先升级成VIP!");
        }*/

        if((int)$user['is_dis']<=0 || (int)$user['dis_level']<=0){
            throw new MyException('您未购买分销会员');
        }
        $distribution_cat = $this->distributionCatService->detail($user['dis_level']);
        if(!$distribution_cat || (empty($distribution_cat['level_1']) && empty($distribution_cat['level_2']))){
            throw new MyException('分销会员级别参数错误');
        }
        if((int)$distribution_cat['level_1']>100 || (int)$distribution_cat['level_2']>100){
            throw new MyException('分销会员级别参数错误');
        }
        $user['level_1'] = (int)$distribution_cat['level_1'];
        $user['level_2'] = (int)$distribution_cat['level_2'];
        $request->user = $user;
        return $next($request);
    }
}

<?php
declare(strict_types=1);

namespace App\Http\Requests\Citui;

class UserRelationRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [];
        
        // 根据不同的路由和方法设置不同的验证规则
        if ($this->isMethod('get')) {
            // 获取关系列表的验证规则
            $rules = [
                'type' => 'sometimes|in:follow,fans',
                'status' => 'sometimes|in:accepted,pending,rejected',
                'search' => 'sometimes|string|max:50',
                'sort_by' => 'sometimes|in:created_at,updated_at,nickname',
                'sort_order' => 'sometimes|in:asc,desc',
                'per_page' => 'sometimes|integer|min:1|max:50'
            ];
        } elseif ($this->isMethod('post') || $this->isMethod('put')) {
            // 关注/取消关注的验证规则
            $rules = [
                'action' => 'required|in:follow,unfollow'
            ];
        }
        
        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return array_merge(parent::messages(), [
            'type.in' => '关系类型只能是 follow 或 fans',
            'status.in' => '状态只能是 accepted、pending 或 rejected',
            'action.required' => '请指定操作类型',
            'action.in' => '操作类型只能是 follow 或 unfollow',
            'sort_by.in' => '排序字段无效',
            'sort_order.in' => '排序方向只能是 asc 或 desc',
            'per_page.min' => '每页数量不能少于1',
            'per_page.max' => '每页数量不能超过50'
        ]);
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return array_merge(parent::attributes(), [
            'type' => '关系类型',
            'status' => '状态',
            'search' => '搜索关键词',
            'action' => '操作类型',
            'sort_by' => '排序字段',
            'sort_order' => '排序方向',
            'per_page' => '每页数量'
        ]);
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // 确保用户已登录
        if (!auth()->check()) {
            abort(401, '用户未登录');
        }
        
        // 设置默认值
        if ($this->isMethod('get')) {
            $this->merge([
                'type' => $this->input('type', 'follow'),
                'sort_by' => $this->input('sort_by', 'created_at'),
                'sort_order' => $this->input('sort_order', 'desc'),
                'per_page' => $this->input('per_page', 15)
            ]);
        }
    }
}
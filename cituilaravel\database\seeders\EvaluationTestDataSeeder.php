<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EvaluationTestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 清空现有数据
        DB::table('zc_evaluation_reports')->truncate();
        DB::table('zc_apps')->truncate();

        // 创建测试APP数据
        $apps = [
            [
                'id' => 1,
                'category_id' => 1,
                'app_name' => '金银合合',
                'app_package' => 'com.jinyinhehe.app',
                'app_version' => '1.0.0',
                'developer' => '金银合合科技',
                'app_label' => '热门,自动,简单',
                'app_size' => 25600000,
                'download_url' => 'https://example.com/download/jinyinhehe.apk',
                'logo_url' => '/upload/image/20241118/jinyinhehe_logo.png',
                'description' => '金银合合是一款简单易用的合成游戏，通过合成获得收益',
                'rating' => 4.7,
                'rating_count' => 1250,
                'download_count' => 2360,
                'view_count' => 5680,
                'status' => 1,
                'is_featured' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            [
                'id' => 2,
                'category_id' => 4,
                'app_name' => '趣步多多',
                'app_package' => 'com.qubuduoduo.app',
                'app_version' => '2.1.0',
                'developer' => '趣步科技',
                'app_label' => '走路赚钱,手动,稳定',
                'app_size' => 18900000,
                'download_url' => 'https://example.com/download/qubuduoduo.apk',
                'logo_url' => '/upload/image/20241118/qubuduoduo_logo.png',
                'description' => '趣步多多是一款走路赚钱应用，每天走路就能获得收益',
                'rating' => 4.5,
                'rating_count' => 890,
                'download_count' => 1860,
                'view_count' => 3420,
                'status' => 1,
                'is_featured' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            [
                'id' => 3,
                'category_id' => 2,
                'app_name' => '短剧星球',
                'app_package' => 'com.duanjuxingqiu.app',
                'app_version' => '3.2.1',
                'developer' => '星球传媒',
                'app_label' => '爆款,秒提现,推荐',
                'app_size' => 45200000,
                'download_url' => 'https://example.com/download/duanjuxingqiu.apk',
                'logo_url' => '/upload/image/20241118/duanjuxingqiu_logo.png',
                'description' => '短剧星球汇聚热门短剧，看剧赚钱两不误',
                'rating' => 4.9,
                'rating_count' => 2150,
                'download_count' => 3560,
                'view_count' => 8920,
                'status' => 1,
                'is_featured' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            [
                'id' => 4,
                'category_id' => 3,
                'app_name' => '阅读赚',
                'app_package' => 'com.yueduzhuan.app',
                'app_version' => '1.5.2',
                'developer' => '阅读科技',
                'app_label' => '稳定,免费体验,新人',
                'app_size' => 12800000,
                'download_url' => 'https://example.com/download/yueduzhuan.apk',
                'logo_url' => '/upload/image/20241118/yueduzhuan_logo.png',
                'description' => '阅读赚是一款阅读赚钱应用，阅读文章即可获得收益',
                'rating' => 4.6,
                'rating_count' => 760,
                'download_count' => 2120,
                'view_count' => 4580,
                'status' => 1,
                'is_featured' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            [
                'id' => 5,
                'category_id' => 1,
                'app_name' => '游戏红包',
                'app_package' => 'com.youxihongbao.app',
                'app_version' => '2.0.3',
                'developer' => '红包游戏',
                'app_label' => '有趣,手动,高收益',
                'app_size' => 32100000,
                'download_url' => 'https://example.com/download/youxihongbao.apk',
                'logo_url' => '/upload/image/20241118/youxihongbao_logo.png',
                'description' => '游戏红包是一款游戏赚钱应用，玩游戏领红包',
                'rating' => 4.4,
                'rating_count' => 520,
                'download_count' => 1920,
                'view_count' => 3680,
                'status' => 1,
                'is_featured' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]
        ];

        DB::table('zc_apps')->insert($apps);

        // 创建测试评测报告数据
        $reports = [
            [
                'id' => 1,
                'app_id' => 1,
                'user_id' => 1,
                'report_title' => '金银合合 评测报告',
                'report_content' => '这是一款非常不错的合成游戏，操作简单，收益稳定。新人福利丰厚，提现门槛低，推荐下载体验。',
                'download_url' => 'https://example.com/download/jinyinhehe.apk',
                'pingfen' => 5,
                'yunxingmoshi' => 1,
                'xinrenfuli' => 0.25,
                'tixianmenkan' => 0.10,
                'dingbaojine' => 2.00,
                'ceshitiaoshu' => 5,
                'ceshishouyi' => 1.25,
                'ceshishichang' => 2.5,
                'ceshishebei' => 'iPhone 14',
                'cepingren' => '测评师001',
                'status' => 1,
                'view_count' => 156,
                'like_count' => 23,
                'is_featured' => 1,
                'cepingriqi' => Carbon::now()->subDays(1),
                'shouyi_1' => 0.25,
                'shouyi_2' => 0.25,
                'shouyi_3' => 0.25,
                'shouyi_4' => 0.25,
                'shouyi_5' => 0.25,
                'pic_main' => '/upload/image/20241118/jinyinhehe_main.jpg',
                'pic_tixian' => '/upload/image/20241118/jinyinhehe_tixian.jpg',
                'pic_daozhang' => '/upload/image/20241118/jinyinhehe_daozhang.jpg',
                'submitted_at' => Carbon::now()->subDays(1),
                'approved_at' => Carbon::now()->subDays(1),
                'created_at' => Carbon::now()->subDays(1),
                'updated_at' => Carbon::now()->subDays(1)
            ],
            [
                'id' => 2,
                'app_id' => 2,
                'user_id' => 1,
                'report_title' => '趣步多多 评测报告',
                'report_content' => '走路赚钱类应用，需要手动操作，但收益还算可以。提现门槛适中，适合喜欢运动的用户。',
                'download_url' => 'https://example.com/download/qubuduoduo.apk',
                'pingfen' => 4,
                'yunxingmoshi' => 2,
                'xinrenfuli' => 0.50,
                'tixianmenkan' => 0.50,
                'dingbaojine' => 3.00,
                'ceshitiaoshu' => 3,
                'ceshishouyi' => 1.50,
                'ceshishichang' => 4.0,
                'ceshishebei' => 'Android 小米13',
                'cepingren' => '测评师002',
                'status' => 1,
                'view_count' => 89,
                'like_count' => 12,
                'is_featured' => 0,
                'cepingriqi' => Carbon::now()->subDays(2),
                'shouyi_1' => 0.50,
                'shouyi_2' => 0.50,
                'shouyi_3' => 0.50,
                'shouyi_4' => 0.00,
                'shouyi_5' => 0.00,
                'pic_main' => '/upload/image/20241118/qubuduoduo_main.jpg',
                'pic_tixian' => '/upload/image/20241118/qubuduoduo_tixian.jpg',
                'pic_daozhang' => '/upload/image/20241118/qubuduoduo_daozhang.jpg',
                'submitted_at' => Carbon::now()->subDays(2),
                'approved_at' => Carbon::now()->subDays(2),
                'created_at' => Carbon::now()->subDays(2),
                'updated_at' => Carbon::now()->subDays(2)
            ],
            [
                'id' => 3,
                'app_id' => 3,
                'user_id' => 1,
                'report_title' => '短剧星球 评测报告',
                'report_content' => '短剧内容丰富，画质清晰，看剧的同时还能赚钱。新人福利很给力，秒提现功能很棒！',
                'download_url' => 'https://example.com/download/duanjuxingqiu.apk',
                'pingfen' => 5,
                'yunxingmoshi' => 1,
                'xinrenfuli' => 1.00,
                'tixianmenkan' => 0.01,
                'dingbaojine' => 10.00,
                'ceshitiaoshu' => 8,
                'ceshishouyi' => 4.00,
                'ceshishichang' => 3.0,
                'ceshishebei' => 'iPhone 15 Pro',
                'cepingren' => '测评师003',
                'status' => 1,
                'view_count' => 234,
                'like_count' => 45,
                'is_featured' => 1,
                'cepingriqi' => Carbon::now()->subDays(3),
                'shouyi_1' => 0.50,
                'shouyi_2' => 0.50,
                'shouyi_3' => 0.50,
                'shouyi_4' => 0.50,
                'shouyi_5' => 2.00,
                'pic_main' => '/upload/image/20241118/duanjuxingqiu_main.jpg',
                'pic_tixian' => '/upload/image/20241118/duanjuxingqiu_tixian.jpg',
                'pic_daozhang' => '/upload/image/20241118/duanjuxingqiu_daozhang.jpg',
                'submitted_at' => Carbon::now()->subDays(3),
                'approved_at' => Carbon::now()->subDays(3),
                'created_at' => Carbon::now()->subDays(3),
                'updated_at' => Carbon::now()->subDays(3)
            ]
        ];

        DB::table('zc_evaluation_reports')->insert($reports);

        $this->command->info('评测测试数据创建成功！');
    }
}

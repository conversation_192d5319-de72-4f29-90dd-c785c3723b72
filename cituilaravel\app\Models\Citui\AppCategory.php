<?php
declare(strict_types=1);

namespace App\Models\Citui;

use App\Models\BaseModel;

class AppCategory extends BaseModel
{
    protected $table = 'app_categories';

    protected $primaryKey = 'category_id';

    protected $fillable = [
        'category_name',
        'category_code',
        'parent_id',
        'category_icon',
        'sort_order',
        'is_active',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 关联APP
     */
    public function apps()
    {
        return $this->hasMany(App::class, 'category_id', 'category_id');
    }
}
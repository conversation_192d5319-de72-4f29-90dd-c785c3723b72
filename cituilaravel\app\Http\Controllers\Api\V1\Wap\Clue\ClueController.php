<?php
declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Wap\Clue;

use App\Http\Controllers\Api\Controller;
use App\Service\Clue\ClueService;
use App\Service\App\AppService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ClueController extends Controller
{
    protected ClueService $clueService;
    protected AppService $appService;

    public function __construct(ClueService $clueService, AppService $appService)
    {
        $this->clueService = $clueService;
        $this->appService = $appService;
        parent::__construct();
    }

    /**
     * 获取APP列表（用于下拉选择）
     * @return JsonResponse
     */
    public function getAppList(): JsonResponse
    {
        try {
            $data = $this->appService->getAppListForSelect();
            return $this->apiSuccess($data);
        } catch (\Exception $e) {
            return $this->apiError($e->getMessage());
        }
    }

    /**
     * 提交线索
     * @param Request $request
     * @return JsonResponse
     */
    public function submitClue(Request $request): JsonResponse
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'app_id' => 'required|integer|min:1',
            'release_time' => 'nullable|integer',
            'clue_money' => 'nullable|numeric|min:0',
            'package_amount' => 'required|string|in:0.1-0.29,0.3-0.49,0.5-0.99,1以上',
            'device_model' => 'required|string|max:100',
            'pic_tixian' => 'nullable|string|max:255',
            'pic_daozhang' => 'nullable|string|max:255',
            'clue_description' => 'required|string|max:1000'
        ], [
            'app_id.required' => '请选择APP',
            'app_id.integer' => 'APP ID格式错误',
            'app_id.min' => '请选择有效的APP',
            'clue_money.numeric' => '放水金额格式错误',
            'clue_money.min' => '放水金额不能为负数',
            'package_amount.required' => '请选择单包大小',
            'package_amount.in' => '单包大小选择无效',
            'device_model.required' => '请输入设备型号',
            'device_model.max' => '设备型号不能超过100个字符',
            'pic_tixian.max' => 'APP提现记录截图路径过长',
            'pic_daozhang.max' => '微信到账记录截图路径过长',
            'clue_description.required' => '请填写线索描述',
            'clue_description.max' => '线索描述不能超过1000个字符'
        ]);

        if ($validator->fails()) {
            return $this->apiError($validator->errors()->first());
        }

        try {
            $data = $this->clueService->submitClue($request->all());
            return $this->apiSuccess($data, '线索提交成功');
        } catch (\Exception $e) {
            return $this->apiError($e->getMessage());
        }
    }

    /**
     * 获取用户的线索列表
     * @return JsonResponse
     */
    public function getUserClues(): JsonResponse
    {
        try {
            $data = $this->clueService->getUserClues();
            return $this->apiSuccess($data);
        } catch (\Exception $e) {
            return $this->apiError($e->getMessage());
        }
    }
}

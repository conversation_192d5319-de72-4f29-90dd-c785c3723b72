<!DOCTYPE html><html lang="zh-CN"><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>红包评测App原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.iconify.design/iconify-icon/2.0.0/iconify-icon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('https://fonts.googleapis.com/css2?family=Alibaba+PuHui+Ti&display=swap');
        }
        body {
            background-color: #f5f5f5;
            padding: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 35px;
            justify-content: center;
            font-family: 'AlibabaPuHuiTi', -apple-system, sans-serif;
        }
        .app-container {
            width: 375px;
            height: 812px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            position: relative;
        }
        .fixed-section { 
            flex-shrink: 0; 
        }
        .content-flex { 
            flex: 1; 
            overflow-y: auto; 
            min-height: 0; 
        }
        .hide-scrollbar::-webkit-scrollbar { 
            display: none; 
        }
        .hide-scrollbar { 
            -ms-overflow-style: none; 
            scrollbar-width: none; 
        }
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .primary-gradient-btn {
            background: linear-gradient(to right, #3B82F6, #2563EB);
            color: white;
            border: none;
            border-radius: 24px;
            font-weight: 600;
        }
        .gold-border-btn {
            border: 2px solid #3B82F6;
            border-radius: 24px;
            background: transparent;
            color: #3B82F6;
            font-weight: 600;
        }
        .pulse-btn {
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(59,130,246,0.7); }
            70% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(59,130,246,0); }
            100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(59,130,246,0); }
        }
        .rotation-animation {
            animation: rotation 5s infinite linear;
        }
        @keyframes rotation {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .flip-card {
            position: relative;
            transform-style: preserve-3d;
            transition: transform 0.8s;
        }
        .flip-card:hover {
            transform: rotateY(180deg);
        }
        .flip-front, .flip-back {
            backface-visibility: hidden;
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 8px;
        }
        .flip-back {
            transform: rotateY(180deg);
            background: linear-gradient(135deg, #FFD700, #FFA500);
        }
        
        /* VIP购买页面样式 */
        .payment-tab {
            transition: all 0.3s ease;
        }
        
        .payment-tab.active {
            background-color: white;
            color: #3B82F6;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>

<!-- 启动页 -->
<div class="app-container relative bg-gradient-to-br from-blue-700 to-blue-900 flex flex-col justify-center items-center">
    <div class="text-center z-10">
        <iconify-icon icon="mdi:coin" class="text-6xl text-white"></iconify-icon>
        <h1 class="text-3xl font-bold mt-4 text-white">红包评测</h1>
        <p class="text-white mt-4 opacity-90">掌握赚钱先机</p>
    </div>
    <p class="absolute bottom-5 text-xs text-white opacity-70">©2025 次推科技</p>
</div>

<!-- 首页（未登录） -->
<div class="app-container">
    <!-- 顶部导航 -->
    <div class="fixed-section bg-white py-3 px-4 flex items-center justify-between border-b border-gray-100">
        <iconify-icon icon="mdi:account-key" class="text-2xl text-gray-700" title="管理员登录" id="admin-login-btn"></iconify-icon>
        <div class="flex-1 mx-4 relative">
            <input type="text" placeholder="搜索APP或任务" class="w-full bg-gray-100 rounded-full py-2 px-4 text-sm focus:outline-none">
            <iconify-icon icon="mdi:magnify" class="absolute right-3 top-2.5 text-gray-500"></iconify-icon>
        </div>
        <iconify-icon icon="mdi:bell-outline" class="text-2xl text-gray-700"></iconify-icon>
    </div>

    <div class="content-flex hide-scrollbar px-4 pt-2 bg-gray-50">
        <!-- 分类导航专区 -->
        <div class="flex justify-between mt-3 mb-2">
            <a href="#" class="flex-1 bg-white border border-gray-200 rounded-lg py-2.5 px-2 text-center text-sm font-medium mx-1" id="test-phone-link">
                <iconify-icon icon="mdi:cellphone" class="text-red-500 mr-1"></iconify-icon>
                积分商城
            </a>
            <a href="#" class="flex-1 bg-white border border-gray-200 rounded-lg py-2.5 px-2 text-center text-sm font-medium mx-1">
                <iconify-icon icon="mdi:sim" class="text-blue-500 mr-1"></iconify-icon>
                流量卡
            </a>
            <a href="#" class="flex-1 bg-white border border-gray-200 rounded-lg py-2.5 px-2 text-center text-sm font-medium mx-1">
                <iconify-icon icon="mdi:headset" class="text-green-500 mr-1"></iconify-icon>
                联系我们
            </a>
        </div>
        
        <!-- Banner轮播 -->
        <div class="h-40 rounded-xl overflow-hidden relative mt-2">
            <div class="bg-gradient-to-r from-blue-700 to-blue-900 h-full flex items-center px-6">
                <div>
                    <h3 class="text-xl font-bold text-white">加入次推，解锁全量APP评测</h3>
                    <p class="text-xs text-white mt-1">开启居家副业之路</p>
                </div>
            </div>
        </div>

        <!-- 会员特权 -->
        <div class="mt-6">
            <h2 class="text-lg font-bold">开启赚钱加速通道</h2>
            <div class="card mt-3 border border-red-500 p-4">
                <div class="flex items-center text-red-500 gap-2">
                    <iconify-icon icon="mdi:diamond" class="text-xl"></iconify-icon>
                    <span class="font-semibold">VIP专享特权</span>
                </div>
                <ul class="mt-3 text-sm space-y-1.5">
                    <li class="flex items-center"><span class="w-2 h-2 rounded-full bg-red-500 mr-2"></span>解锁全量评测</li>
                    <li class="flex items-center"><span class="w-2 h-2 rounded-full bg-red-500 mr-2"></span>每日放水线索</li>
                    <li class="flex items-center"><span class="w-2 h-2 rounded-full bg-red-500 mr-2"></span>邀请立赚500元</li>
                </ul>
                <div class="flex justify-between items-center mt-4">
                    <span class="font-bold text-amber-600">¥3980/年</span>
                    <button class="primary-gradient-btn px-5 py-2 text-sm pulse-btn" id="vip-btn">立即开通</button>
                </div>
            </div>
        </div>

        <!-- 免费体验区 -->
        <div class="mt-6 mb-8">
            <h2 class="text-lg font-bold">新手免费试看</h2>
            <div class="space-y-4 mt-3">
                <!-- 卡片1 -->
                <div class="card p-4 bg-gradient-to-r from-red-50 to-white">
                    <div class="flex justify-between">
                        <div class="flex items-center">
                            <iconify-icon icon="mdi:coin-outline" class="text-xl text-amber-500 mr-2"></iconify-icon>
                            <span class="font-semibold">趣接成语</span>
                        </div>
                        <div class="flex gap-4">
                            <span class="flex items-center text-amber-400">
                                <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>4.6
                            </span>
                            <span class="flex items-center text-gray-600">
                                <iconify-icon icon="mdi:download-outline" class="mr-1"></iconify-icon>1260次
                            </span>
                        </div>
                    </div>
                    <div class="flex gap-2 mt-3">
                        <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">🔥手动</span>
                        <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">⚡￥0.1起提</span>
                        <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">新人0.1</span>
                        <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">顶包￥2</span>
                    </div>
                    <div class="flex justify-between items-center mt-3">
                        <button class="red-gradient-btn px-3 py-1 text-sm">免费试用</button>
                        <button class="text-red-500 font-semibold text-sm flex items-center">
                            查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                        </button>
                    </div>
                </div>
                
                <!-- 卡片2 -->
                <div class="card p-4 bg-gradient-to-r from-blue-50 to-white">
                    <div class="flex justify-between">
                        <div class="flex items-center">
                            <iconify-icon icon="mdi:video" class="text-xl text-red-500 mr-2"></iconify-icon>
                            <span class="font-semibold">番茄短剧</span>
                        </div>
                        <div class="flex gap-4">
                            <span class="flex items-center text-amber-400">
                                <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>4.3
                            </span>
                            <span class="flex items-center text-gray-600">
                                <iconify-icon icon="mdi:download-outline" class="mr-1"></iconify-icon>980次
                            </span>
                        </div>
                    </div>
                    <div class="flex gap-2 mt-3">
                        <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥推荐</span>
                        <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡秒提现</span>
                        <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">免费体验</span>
                    </div>
                    <div class="flex justify-between items-center mt-3">
                        <button class="red-gradient-btn px-3 py-1 text-sm">免费试用</button>
                        <button class="text-red-500 font-semibold text-sm flex items-center">
                            查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                        </button>
                    </div>
                </div>
                
                <!-- 卡片3 -->
                <div class="card p-4 bg-gradient-to-r from-green-50 to-white">
                    <div class="flex justify-between">
                        <div class="flex items-center">
                            <iconify-icon icon="mdi:cash-multiple" class="text-xl text-green-500 mr-2"></iconify-icon>
                            <span class="font-semibold">小小成语库</span>
                        </div>
                        <div class="flex gap-4">
                            <span class="flex items-center text-amber-400">
                                <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>4.2
                            </span>
                            <span class="flex items-center text-gray-600">
                                <iconify-icon icon="mdi:download-outline" class="mr-1"></iconify-icon>760次
                            </span>
                        </div>
                    </div>
                    <div class="flex gap-2 mt-3">
                        <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥简单</span>
                        <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡走路赚钱</span>
                        <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">免费体验</span>
                    </div>
                    <div class="flex justify-between items-center mt-3">
                        <button class="red-gradient-btn px-3 py-1 text-sm">免费试用</button>
                        <button class="text-red-500 font-semibold text-sm flex items-center">
                            查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                        </button>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <button class="text-sm text-blue-500 font-medium flex items-center justify-center mx-auto">
                        查看更多免费试看APP评测 <iconify-icon icon="mdi:chevron-right" class="ml-1"></iconify-icon>
                    </button>
                </div>
            </div>
        </div>
        
        <script>
            // 初始化按钮状态
            document.addEventListener('DOMContentLoaded', function() {
                const appButtons = document.querySelectorAll('.app-action-btn');
                appButtons.forEach(button => {
                    const status = button.getAttribute('data-status');
                    if(status === 'not-downloaded') {
                        button.textContent = '下载赚钱';
                    } else if(status === 'downloaded') {
                        button.textContent = '重新安装';
                    } else if(status === 'installed') {
                        button.textContent = '打开赚钱';
                    }
                });
            });
        </script>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-blue-600">
                <iconify-icon icon="mdi:home" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500 non-vip-link" data-target="evaluation">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">评测</span>
            </a>
            <a href="#" class="flex flex-col items-center">
                <div class="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center -mt-5 shadow-lg">
                    <iconify-icon icon="mdi:water" class="text-2xl text-white"></iconify-icon>
                </div>
                <span class="text-xs mt-1 text-gray-500 non-vip-link" data-target="clue">线索</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-group-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">邀请</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
    
    
</div>

<!-- 会员中心 -->
<div class="app-container">
    <div class="content-flex hide-scrollbar bg-gradient-to-b from-blue-800 to-blue-900 text-white">
        <!-- 头部信息 -->
        <div class="py-8 relative">
            <div class="flex items-center mx-5">
                <div class="w-20 h-20 rounded-full border-2 border-amber-300 overflow-hidden flex-shrink-0">
                    <img src="https://placehold.co/200x200" alt="用户头像" class="w-full h-full object-cover">
                </div>
                <div class="ml-4">
                    <h2 class="font-bold text-xl">用户昵称</h2>
                    <div class="flex items-center mt-1">
                        <span class="text-sm">ID: 238947</span>
                        <button class="ml-2">
                            <iconify-icon icon="mdi:content-copy" class="text-sm"></iconify-icon>
                        </button>
                    </div>
                    <div class="flex items-center mt-1 bg-amber-500/20 px-3 py-1 rounded-full">
                        <iconify-icon icon="mdi:crown" class="text-amber-300 mr-1"></iconify-icon>
                        <span class="text-sm text-amber-300">邀请码: VIP238</span>
                        <button class="ml-2">
                            <iconify-icon icon="mdi:content-copy" class="text-sm"></iconify-icon>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="bg-white/10 mx-5 mt-4 p-3 rounded-lg flex items-center justify-between">
                <div>
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:crown" class="text-amber-300 mr-2"></iconify-icon>
                        <span class="font-semibold">VIP会员</span>
                    </div>
                    <div class="text-sm mt-1">有效期至：2026.07.18</div>
                </div>
                <div class="bg-amber-500/20 px-3 py-1 rounded-full">
                    <span class="text-amber-300 font-medium">剩余365天</span>
                </div>
            </div>
            
            <div class="bg-white/10 mx-5 mt-4 p-3 rounded-lg flex items-center justify-between">
                <div>
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:star-circle" class="text-amber-300 mr-2"></iconify-icon>
                        <span class="font-semibold">积分余额</span>
                    </div>
                    <div class="text-xl font-bold mt-1">200</div>
                </div>
                <button class="bg-amber-500 text-white px-4 py-1.5 rounded-full text-sm font-medium">
                    立即兑换
                </button>
            </div>
        </div>

        <!-- 功能入口 -->
        <div class="mx-5 mt-4">
            <h3 class="font-bold mb-3">功能入口</h3>
            <div class="grid grid-cols-2 gap-3">
                <a class="flex items-center p-4 bg-white/10 rounded-xl" id="my-reports-link">
                    <iconify-icon icon="mdi:file-document" class="text-2xl text-amber-300 mr-3"></iconify-icon>
                    <div>
                        <p class="font-medium">我的报告</p>
                        <p class="text-xs text-gray-300 mt-1">已提交12份</p>
                    </div>
                </a>
                <a class="flex items-center p-4 bg-white/10 rounded-xl">
                    <iconify-icon icon="mdi:water" class="text-2xl text-amber-300 mr-3"></iconify-icon>
                    <div>
                        <p class="font-medium">我的线索</p>
                        <p class="text-xs text-gray-300 mt-1">5条待审核</p>
                    </div>
                </a>
                <script>
                    // 为"我的线索"链接添加点击事件
                    document.addEventListener('DOMContentLoaded', function() {
        // 初始化VIP购买页面功能
        startCountdown();
        setupPaymentTabs();
        setupCodeVerification();
        
        // 筛选弹窗功能
        const filterBtn = document.getElementById('filter-btn');
        const filterPopup = document.getElementById('filter-popup');
        
        if(filterBtn && filterPopup) {
            // 点击筛选按钮显示/隐藏弹窗
            filterBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                filterPopup.classList.toggle('hidden');
            });
            
            // 点击页面其他区域关闭弹窗
            document.addEventListener('click', function(e) {
                if(!filterPopup.contains(e.target) && e.target !== filterBtn) {
                    filterPopup.classList.add('hidden');
                }
            });
            
            // 筛选标签点击切换选中状态
            const filterTags = document.querySelectorAll('.filter-tag');
            filterTags.forEach(tag => {
                tag.addEventListener('click', function() {
                    // 在同一组中，如果点击的不是"全部"，则移除其他标签的选中状态
                    const parentDiv = this.parentElement;
                    const siblingTags = parentDiv.querySelectorAll('.filter-tag');
                    
                    if(this.textContent.trim() === '全部') {
                        // 如果点击"全部"，则只选中"全部"
                        siblingTags.forEach(sibTag => {
                            sibTag.classList.remove('bg-blue-100', 'text-blue-600');
                            sibTag.classList.add('bg-gray-100', 'text-gray-700');
                        });
                        this.classList.remove('bg-gray-100', 'text-gray-700');
                        this.classList.add('bg-blue-100', 'text-blue-600');
                    } else {
                        // 如果点击其他标签，则取消"全部"的选中状态
                        const allTag = Array.from(siblingTags).find(tag => tag.textContent.trim() === '全部');
                        if(allTag) {
                            allTag.classList.remove('bg-blue-100', 'text-blue-600');
                            allTag.classList.add('bg-gray-100', 'text-gray-700');
                        }
                        
                        // 切换当前标签的选中状态
                        this.classList.toggle('bg-blue-100');
                        this.classList.toggle('text-blue-600');
                        this.classList.toggle('bg-gray-100');
                        this.classList.toggle('text-gray-700');
                    }
                });
            });
            
            // 初始化时选中所有"全部"选项
            document.querySelectorAll('.filter-tag').forEach(tag => {
                if(tag.textContent.trim() === '全部') {
                    tag.classList.remove('bg-gray-100', 'text-gray-700');
                    tag.classList.add('bg-blue-100', 'text-blue-600');
                }
            });
            
            // 重置按钮功能
            const resetBtn = filterPopup.querySelector('button:first-of-type');
            if(resetBtn) {
                resetBtn.addEventListener('click', function() {
                    // 重置所有筛选条件
                    document.querySelectorAll('.filter-tag').forEach(tag => {
                        if(tag.textContent.trim() === '全部') {
                            tag.classList.remove('bg-gray-100', 'text-gray-700');
                            tag.classList.add('bg-blue-100', 'text-blue-600');
                        } else {
                            tag.classList.remove('bg-blue-100', 'text-blue-600');
                            tag.classList.add('bg-gray-100', 'text-gray-700');
                        }
                    });
                });
            }
            
            // 确定按钮功能
            const confirmBtn = filterPopup.querySelector('button:last-of-type');
            if(confirmBtn) {
                confirmBtn.addEventListener('click', function() {
                    // 隐藏筛选弹窗
                    filterPopup.classList.add('hidden');
                    // 这里可以添加实际的筛选逻辑，如筛选评测卡片等
                });
            }
        }
                        const myCluesLink = document.querySelectorAll('.flex.items-center.p-4.bg-white\\/10.rounded-xl')[1];
                        if (myCluesLink) {
                            myCluesLink.addEventListener('click', function(e) {
                                e.preventDefault();
                                // 隐藏所有页面
                                const appContainers = document.querySelectorAll('.app-container');
                                appContainers.forEach(container => {
                                    container.style.display = 'none';
                                });
                                // 显示我的线索页面
                                document.querySelectorAll('.app-container')[12].style.display = 'flex';
                            });
                        }
                        
                        // 筛选弹窗功能
                        const filterBtn = document.getElementById('filter-btn');
                        const filterPopup = document.getElementById('filter-popup');
                        
                        if(filterBtn && filterPopup) {
                            // 点击筛选按钮显示/隐藏弹窗
                            filterBtn.addEventListener('click', function(e) {
                                e.stopPropagation();
                                filterPopup.classList.toggle('hidden');
                            });
                            
                            // 点击页面其他区域关闭弹窗
                            document.addEventListener('click', function(e) {
                                if(!filterPopup.contains(e.target) && e.target !== filterBtn) {
                                    filterPopup.classList.add('hidden');
                                }
                            });
                            
                            // 筛选标签点击切换选中状态
                            const filterTags = document.querySelectorAll('.filter-tag');
                            filterTags.forEach(tag => {
                                tag.addEventListener('click', function() {
                                    // 在同一组中，如果点击的不是"全部"，则移除其他标签的选中状态
                                    const parentDiv = this.parentElement;
                                    const siblingTags = parentDiv.querySelectorAll('.filter-tag');
                                    
                                    if(this.textContent.trim() === '全部') {
                                        // 如果点击"全部"，则只选中"全部"
                                        siblingTags.forEach(sibTag => {
                                            sibTag.classList.remove('bg-blue-100', 'text-blue-600');
                                            sibTag.classList.add('bg-gray-100', 'text-gray-700');
                                        });
                                        this.classList.remove('bg-gray-100', 'text-gray-700');
                                        this.classList.add('bg-blue-100', 'text-blue-600');
                                    } else {
                                        // 如果点击其他标签，则取消"全部"的选中状态
                                        const allTag = Array.from(siblingTags).find(tag => tag.textContent.trim() === '全部');
                                        if(allTag) {
                                            allTag.classList.remove('bg-blue-100', 'text-blue-600');
                                            allTag.classList.add('bg-gray-100', 'text-gray-700');
                                        }
                                        
                                        // 切换当前标签的选中状态
                                        this.classList.toggle('bg-blue-100');
                                        this.classList.toggle('text-blue-600');
                                        this.classList.toggle('bg-gray-100');
                                        this.classList.toggle('text-gray-700');
                                    }
                                });
                            });
                            
                            // 初始化时选中所有"全部"选项
                            document.querySelectorAll('.filter-tag').forEach(tag => {
                                if(tag.textContent.trim() === '全部') {
                                    tag.classList.remove('bg-gray-100', 'text-gray-700');
                                    tag.classList.add('bg-blue-100', 'text-blue-600');
                                }
                            });
                            
                            // 重置按钮功能
                            const resetBtn = filterPopup.querySelector('button:first-of-type');
                            if(resetBtn) {
                                resetBtn.addEventListener('click', function() {
                                    // 重置所有筛选条件
                                    document.querySelectorAll('.filter-tag').forEach(tag => {
                                        if(tag.textContent.trim() === '全部') {
                                            tag.classList.remove('bg-gray-100', 'text-gray-700');
                                            tag.classList.add('bg-blue-100', 'text-blue-600');
                                        } else {
                                            tag.classList.remove('bg-blue-100', 'text-blue-600');
                                            tag.classList.add('bg-gray-100', 'text-gray-700');
                                        }
                                    });
                                });
                            }
                            
                            // 确定按钮功能
                            const confirmBtn = filterPopup.querySelector('button:last-of-type');
                            if(confirmBtn) {
                                confirmBtn.addEventListener('click', function() {
                                    // 隐藏筛选弹窗
                                    filterPopup.classList.add('hidden');
                                    // 这里可以添加实际的筛选逻辑，如筛选评测卡片等
                                });
                            }
                        }
                    });
                </script>
                <a class="flex items-center p-4 bg-white/10 rounded-xl" id="points-detail-link">
                    <iconify-icon icon="mdi:star-circle" class="text-2xl text-amber-300 mr-3"></iconify-icon>
                    <div>
                        <p class="font-medium">积分明细</p>
                        <p class="text-xs text-gray-300 mt-1">收支记录</p>
                    </div>
                </a>
                <a class="flex items-center p-4 bg-white/10 rounded-xl" id="vip-code-redemption">
                    <iconify-icon icon="mdi:ticket-confirmation" class="text-2xl text-amber-300 mr-3"></iconify-icon>
                    <div>
                        <p class="font-medium">VIP码兑换</p>
                        <p class="text-xs text-gray-300 mt-1">激活会员</p>
                    </div>
                </a>
                <a class="flex items-center p-4 bg-white/10 rounded-xl">
                    <iconify-icon icon="mdi:account-group" class="text-2xl text-amber-300 mr-3"></iconify-icon>
                    <div>
                        <p class="font-medium">邀请管理</p>
                        <p class="text-xs text-gray-300 mt-1">已邀请17人</p>
                    </div>
                </a>
                <a class="flex items-center p-4 bg-white/10 rounded-xl">
                    <iconify-icon icon="mdi:cog" class="text-2xl text-amber-300 mr-3"></iconify-icon>
                    <div>
                        <p class="font-medium">设置</p>
                        <p class="text-xs text-gray-300 mt-1">退出登录</p>
                    </div>
                </a>
            </div>
        </div>

        <!-- 数据面板 -->
        <div class="mx-5 mt-6 mb-8">
            <h3 class="font-bold mb-3">数据面板</h3>
            <div class="grid grid-cols-2 gap-3">
                <div class="bg-white/10 p-4 rounded-xl">
                    <p class="text-xs text-gray-300">本月通过评测赚取</p>
                    <p class="text-xl font-bold text-amber-300 mt-2">¥2,580</p>
                    <div class="flex items-center mt-2 text-xs">
                        <iconify-icon icon="mdi:arrow-up" class="text-green-400"></iconify-icon>
                        <span class="text-green-400">较上月+15%</span>
                    </div>
                </div>
                <div class="bg-white/10 p-4 rounded-xl">
                    <p class="text-xs text-gray-300">本月线索奖励积分</p>
                    <p class="text-xl font-bold text-amber-300 mt-2">180分</p>
                    <div class="flex items-center mt-2 text-xs">
                        <iconify-icon icon="mdi:arrow-up" class="text-green-400"></iconify-icon>
                        <span class="text-green-400">较上月+8%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:home" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">评测</span>
            </a>
            <a href="#" class="flex flex-col items-center">
                <div class="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center -mt-5 shadow-lg">
                    <iconify-icon icon="mdi:water" class="text-2xl text-white"></iconify-icon>
                </div>
                <span class="text-xs mt-1 text-gray-500">线索</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-group-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">邀请</span>
            </a>
            <a href="#" class="flex flex-col items-center text-blue-600">
                <iconify-icon icon="mdi:account-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
</div>

<!-- 评测列表页 -->
<div class="app-container">
    <div class="fixed-section bg-white py-4 px-4 border-b border-gray-100">
        <!-- 添加的搜索框 -->
        <div class="relative mb-3">
            <input type="text" placeholder="搜索APP或任务" class="w-full bg-gray-100 rounded-full py-2 px-4 text-sm focus:outline-none">
            <iconify-icon icon="mdi:magnify" class="absolute right-3 top-2 text-gray-500"></iconify-icon>
        </div>
        <div class="flex items-center mb-3">
            <div class="flex">
                <button class="font-bold mr-6 border-b-2 border-red-500 pb-2">最新评测</button>
                <button class="font-medium text-gray-500 mr-6 pb-2">已下载</button>
                <button class="font-medium text-gray-500 pb-2">未下载</button>
            </div>
            <div class="ml-auto">
                <button id="filter-btn" class="text-sm flex items-center text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                    筛选
                    <iconify-icon icon="mdi:filter-outline" class="ml-1"></iconify-icon>
                </button>
                
                <!-- 筛选弹窗 -->
                <div id="filter-popup" class="hidden absolute right-4 mt-2 bg-white rounded-lg shadow-lg p-4 z-10 w-64">
                    <div class="mb-3">
                        <p class="font-medium mb-2">APP类型</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">全部</span>
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">合成游戏</span>
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">短剧</span>
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">阅读</span>
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">走路</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <p class="font-medium mb-2">运行模式</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">全部</span>
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">自动</span>
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">手动</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <p class="font-medium mb-2">新人福利</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">全部</span>
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">≥1元</span>
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">0.5-0.99元</span>
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">＜0.5元</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <p class="font-medium mb-2">提现门槛</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">全部</span>
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">≤0.1元</span>
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">0.1-1元</span>
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">＞1元</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <p class="font-medium mb-2">顶包金额</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">全部</span>
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">≥5元</span>
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">2-5元</span>
                            <span class="filter-tag cursor-pointer px-2 py-1 rounded-full bg-gray-100 text-xs">＜2元</span>
                        </div>
                    </div>
                    <div class="flex justify-between mt-4">
                        <button class="text-xs bg-gray-100 text-gray-600 px-3 py-1.5 rounded-lg">重置</button>
                        <button class="text-xs bg-blue-600 text-white px-3 py-1.5 rounded-lg">确定</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 提交评测报告按钮 -->
        <button class="w-full bg-gradient-to-r from-amber-500 to-amber-600 text-white py-3 rounded-lg font-bold flex items-center justify-center mb-2">
            <iconify-icon icon="mdi:plus-circle" class="mr-2 text-xl"></iconify-icon>
            提交评测报告赚取积分
        </button>
    </div>

    <div class="content-flex hide-scrollbar px-4 pt-2">
        <!-- 高收益专区 -->
        <div class="bg-amber-50 border border-amber-200 rounded-lg px-4 py-3 mt-3">
            <h3 class="font-bold flex items-center text-red-600">
                <iconify-icon icon="mdi:fire" class="mr-1 text-xl text-red-500"></iconify-icon>
                今日放水推荐
            </h3>
            <ul class="mt-2 space-y-2 pl-5">
                <li class="text-sm">《金币大师》新用户+8元（11-13点）</li>
                <li class="text-sm">《短剧星球》看剧双倍收益</li>
                <li class="text-sm">《阅读赚》今日签到翻倍</li>
            </ul>
        </div>

        <!-- 评测卡片 -->
        <div class="mt-6 space-y-4 pb-6">
            <!-- 卡片1 -->
            <div class="card p-4 bg-gradient-to-r from-red-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center flex-col items-start">
                        <div class="flex items-center">
                            <iconify-icon icon="mdi:coin-outline" class="text-xl text-amber-500 mr-2"></iconify-icon>
                            <span class="font-semibold">金银合合</span>
                        </div>
                        <span class="text-xs text-gray-500 mt-0.5">2025-05-13 更新</span>
                    </div>
                    <div class="flex gap-4">
                        <span class="flex items-center text-amber-400">
                            <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>4.7
                        </span>
                        <span class="flex items-center text-gray-600">
                            <iconify-icon icon="mdi:download-outline" class="mr-1"></iconify-icon>2360次
                        </span>
                    </div>
                </div>
                <div class="flex gap-2 mt-3">
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥自动</span>
                    <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡$0.1起提</span>
                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">新人$0.25</span>
                    <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">顶包￥2</span>
                </div>
                <div class="flex justify-between items-center mt-3">
                    <button class="app-action-btn red-gradient-btn px-3 py-1 text-sm" data-status="not-downloaded">下载赚钱</button>
                    <button class="text-red-500 font-semibold text-sm flex items-center">
                        查看完整报告 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
            
            <!-- 卡片2 -->
            <div class="card p-4 bg-gradient-to-r from-blue-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center flex-col items-start">
                        <div class="flex items-center">
                            <iconify-icon icon="mdi:cash-multiple" class="text-xl text-green-500 mr-2"></iconify-icon>
                            <span class="font-semibold">趣步多多</span>
                        </div>
                        <span class="text-xs text-gray-500 mt-0.5">2025-05-12 更新</span>
                    </div>
                    <div class="flex gap-4">
                        <span class="flex items-center text-amber-400">
                            <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>4.5
                        </span>
                        <span class="flex items-center text-gray-600">
                            <iconify-icon icon="mdi:download-outline" class="mr-1"></iconify-icon>1860次
                        </span>
                    </div>
                </div>
                <div class="flex gap-2 mt-3">
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥高佣</span>
                    <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡$0.5起提</span>
                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">走路赚钱</span>
                    <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">首单￥3</span>
                </div>
                <div class="flex justify-between items-center mt-3">
                    <button class="app-action-btn red-gradient-btn px-3 py-1 text-sm" data-status="downloaded">重新安装</button>
                    <button class="text-red-500 font-semibold text-sm flex items-center">
                        查看完整报告 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                    </button>

                </div>
            </div>
            
            <!-- 卡片3 -->
            <div class="card p-4 bg-gradient-to-r from-amber-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center flex-col items-start">
                        <div class="flex items-center">
                            <iconify-icon icon="mdi:video" class="text-xl text-red-500 mr-2"></iconify-icon>
                            <span class="font-semibold">短剧星球</span>
                        </div>
                        <span class="text-xs text-gray-500 mt-0.5">2025-05-12 更新</span>
                    </div>
                    <div class="flex gap-4">
                        <span class="flex items-center text-amber-400">
                            <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>4.9
                        </span>
                        <span class="flex items-center text-gray-600">
                            <iconify-icon icon="mdi:download-outline" class="mr-1"></iconify-icon>3560次
                        </span>
                    </div>
                </div>
                <div class="flex gap-2 mt-3">
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥爆款</span>
                    <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡秒提现</span>
                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">新人$1.0</span>
                    <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">首充返￥10</span>
                </div>
                <div class="flex justify-between items-center mt-3">
                    <button class="app-action-btn red-gradient-btn px-3 py-1 text-sm" data-status="installed">打开赚钱</button>
                    <button class="text-red-500 font-semibold text-sm flex items-center">
                        查看完整报告 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
            
            <!-- 卡片4 -->
            <div class="card p-4 bg-gradient-to-r from-green-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center flex-col items-start">
                        <div class="flex items-center">
                            <iconify-icon icon="mdi:book-open-variant" class="text-xl text-blue-500 mr-2"></iconify-icon>
                            <span class="font-semibold">阅读赚</span>
                        </div>
                        <span class="text-xs text-gray-500 mt-0.5">2025-05-11 更新</span>
                    </div>
                    <div class="flex gap-4">
                        <span class="flex items-center text-amber-400">
                            <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>4.6
                        </span>
                        <span class="flex items-center text-gray-600">
                            <iconify-icon icon="mdi:download-outline" class="mr-1"></iconify-icon>2120次
                        </span>
                    </div>
                </div>
                <div class="flex gap-2 mt-3">
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥稳定</span>
                    <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡$1起提</span>
                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">新人$0.5</span>
                    <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">签到￥0.5</span>
                </div>
                <div class="flex justify-between items-center mt-3">
                    <button class="app-action-btn red-gradient-btn px-3 py-1 text-sm" data-status="not-downloaded">下载赚钱</button>
                    <button class="text-red-500 font-semibold text-sm flex items-center">
                        查看完整报告 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
            
            <!-- 卡片5 -->
            <div class="card p-4 bg-gradient-to-r from-purple-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center flex-col items-start">
                        <div class="flex items-center">
                            <iconify-icon icon="mdi:gamepad-variant" class="text-xl text-purple-500 mr-2"></iconify-icon>
                            <span class="font-semibold">游戏红包</span>
                        </div>
                        <span class="text-xs text-gray-500 mt-0.5">2025-05-10 更新</span>
                    </div>
                    <div class="flex gap-4">
                        <span class="flex items-center text-amber-400">
                            <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>4.4
                        </span>
                        <span class="flex items-center text-gray-600">
                            <iconify-icon icon="mdi:download-outline" class="mr-1"></iconify-icon>1920次
                        </span>
                    </div>
                </div>
                <div class="flex gap-2 mt-3">
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥有趣</span>
                    <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡$3起提</span>
                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">新人$0.8</span>
                    <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">邀请￥5</span>
                </div>
                <div class="flex justify-between items-center mt-3">
                    <button class="app-action-btn red-gradient-btn px-3 py-1 text-sm" data-status="not-downloaded">下载赚钱</button>
                    <button class="text-red-500 font-semibold text-sm flex items-center">
                        查看完整报告 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:home" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-blue-600">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">评测</span>
            </a>
            <a href="#" class="flex flex-col items-center">
                <div class="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center -mt-5 shadow-lg">
                    <iconify-icon icon="mdi:water" class="text-2xl text-white"></iconify-icon>
                </div>
                <span class="text-xs mt-1 text-gray-500">线索</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-group-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">邀请</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
</div>

<!-- 评测详情页 -->
<div class="app-container">
    <div class="fixed-section bg-white py-3 px-4 border-b border-gray-100">
        <div class="flex items-center">
            <iconify-icon icon="mdi:arrow-left" class="text-2xl text-gray-700"></iconify-icon>
        </div>
    </div>

    <div class="content-flex hide-scrollbar px-4 pt-2">
        <!-- 头部信息 -->
        <div class="flex items-center px-2 pt-4">
            <img id="1" src="https://modao.cc/ai/uploads/ai_pics/3/30532/aigp_1752851807.jpeg" alt="App Logo" class="w-14 h-14 rounded-lg">
            <div class="ml-4 flex-1">
                <div class="flex justify-between items-start">
                    <h2 class="font-bold text-lg">金币大师</h2>
                    <div class="flex items-center text-gray-600">
                        <iconify-icon icon="mdi:download-outline" class="mr-1"></iconify-icon>
                        <span>3560次</span>
                    </div>
                </div>
                <div class="mt-1 flex flex-wrap gap-2">
                    <div class="flex items-center text-amber-400">
                        <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>
                        <span>4.7分（合成小游戏）</span>
                    </div>
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥自动</span>
                    <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡新人￥0.25</span>
                    <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">￥0.1起提</span>
                    <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">顶包￥2</span>
                </div>
            </div>
        </div>

        <!-- 数据看板 -->
        <div class="card p-4 mt-5">
            <h3 class="font-bold mb-3">测试数据报告</h3>
            <div id="chart" class="h-40"></div>
            <div class="grid grid-cols-2 gap-2 mt-4 text-center">
                <div class="bg-gray-50 py-3 rounded">
                    <p class="text-xs text-gray-500">测试条数</p>
                    <p class="font-semibold">10条</p>
                </div>
                <div class="bg-gray-50 py-3 rounded">
                    <p class="text-xs text-gray-500">测试收益</p>
                    <p class="font-semibold">¥2.5</p>
                </div>
                <div class="bg-gray-50 py-3 rounded">
                    <p class="text-xs text-gray-500">6分钟</p>
                    <p class="font-semibold">测试时长</p>
                </div>
                <div class="bg-gray-50 py-3 rounded">
                    <p class="text-xs text-gray-500">测试设备</p>
                    <p class="font-semibold">华为mate30 5G</p>
                </div>
            </div>
            <a href="#" class="block text-center bg-blue-500 text-white py-2 rounded-lg mt-3 hover:bg-blue-600 transition" id="buy-same-device">购买同款设备</a>
            
            <div class="mt-5">
                <h3 class="font-bold mb-3">三重验证真机实测</h3>
                <div class="grid grid-cols-3 gap-2">
                    <div class="rounded-lg overflow-hidden">
                        <img src="https://placehold.co/200x300/FFD700/333333?text=%E6%B8%B8%E6%88%8F%E4%B8%BB%E7%95%8C%E9%9D%A2" alt="游戏主界面" class="w-full h-auto">
                        <p class="text-xs text-center mt-1">游戏主界面</p>
                    </div>
                    <div class="rounded-lg overflow-hidden">
                        <img src="https://placehold.co/200x300/FFD700/333333?text=%E6%8F%90%E7%8E%B0%E8%AE%B0%E5%BD%95" alt="APP内提现记录界面" class="w-full h-auto">
                        <p class="text-xs text-center mt-1">APP内提现记录</p>
                    </div>
                    <div class="rounded-lg overflow-hidden">
                        <img src="https://placehold.co/200x300/FFD700/333333?text=%E5%BE%AE%E4%BF%A1%E5%88%B0%E8%B4%A6" alt="微信到账记录界面" class="w-full h-auto">
                        <p class="text-xs text-center mt-1">微信到账记录</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- VIP秘籍 -->
        <div class="card mt-5 p-4">
            <div class="flex items-center">
                <iconify-icon icon="mdi:lock" class="text-xl text-amber-500 mr-2"></iconify-icon>
                <h3 class="font-bold">测评报告</h3>
            </div>
            <div class="flex justify-between text-xs text-gray-500 mt-1 mb-2 pl-8">
                <span>评测时间：2025-05-09</span>
                <span>评测人：陈评测</span>
            </div>
            <div class="pl-8 mt-3">
                <p class="text-sm">每天10-12点收益翻倍</p>
                <p class="text-sm mt-1">分享给2好友解锁无限提现</p>
            </div>
        </div>

        <!-- 放水记录 -->
        <div class="card mt-5 p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <iconify-icon icon="mdi:water" class="text-xl text-blue-500 mr-2"></iconify-icon>
                    <h3 class="font-bold">放水记录</h3>
                </div>
                <button class="text-xs bg-amber-100 text-amber-600 px-2 py-1 rounded-full flex items-center">
                    <iconify-icon icon="mdi:plus-circle" class="mr-1"></iconify-icon>
                    提交放水线索赚积分
                </button>
            </div>
            <div class="mt-3">
                <div class="flex items-center py-2 border-b border-gray-100">
                    <div class="w-8 h-8 rounded-full bg-gray-200 overflow-hidden flex-shrink-0">
                        <img src="https://placehold.co/40x40" alt="用户头像" class="w-full h-full object-cover">
                    </div>
                    <div class="ml-2 flex-1">
                        <div class="flex justify-between">
                            <span class="font-medium text-sm">用户8273 <span class="text-xs text-green-500">[已实名]</span></span>
                            <span class="text-xs bg-red-100 text-red-600 px-2 py-0.5 rounded-full">放水1.2元</span>
                        </div>
                        <p class="text-sm mt-1 text-gray-700">新人秒到，实名后提现速度快</p>
                        <div class="flex justify-between mt-1">
                            <span class="text-xs text-gray-500">华为mate40 Pro</span>
                            <span class="text-xs text-gray-500">2025-05-12 11:23</span>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center py-2 border-b border-gray-100">
                    <div class="w-8 h-8 rounded-full bg-gray-200 overflow-hidden flex-shrink-0">
                        <img src="https://placehold.co/40x40" alt="用户头像" class="w-full h-full object-cover">
                    </div>
                    <div class="ml-2 flex-1">
                        <div class="flex justify-between">
                            <span class="font-medium text-sm">用户6521 <span class="text-xs text-orange-500">[未实名]</span></span>
                            <span class="text-xs bg-orange-100 text-orange-600 px-2 py-0.5 rounded-full">放水0.5元</span>
                        </div>
                        <p class="text-sm mt-1 text-gray-700">首次提现需要等待3小时</p>
                        <div class="flex justify-between mt-1">
                            <span class="text-xs text-gray-500">iPhone 15 Pro</span>
                            <span class="text-xs text-gray-500">2025-05-11 16:45</span>
                        </div>
                        <div class="grid grid-cols-2 gap-2 mt-2">
                            <img src="https://placehold.co/200x150/FFD700/333333?text=APP%E6%8F%90%E7%8E%B0%E8%AE%B0%E5%BD%95" alt="APP提现记录" class="w-full h-auto rounded-lg">
                            <img src="https://placehold.co/200x150/90EE90/333333?text=%E5%BE%AE%E4%BF%A1%E5%88%B0%E8%B4%A6%E8%AE%B0%E5%BD%95" alt="微信到账记录" class="w-full h-auto rounded-lg">
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center py-2">
                    <div class="w-8 h-8 rounded-full bg-gray-200 overflow-hidden flex-shrink-0">
                        <img src="https://placehold.co/40x40" alt="用户头像" class="w-full h-full object-cover">
                    </div>
                    <div class="ml-2 flex-1">
                        <div class="flex justify-between">
                            <span class="font-medium text-sm">用户3927 <span class="text-xs text-green-500">[已实名]</span></span>
                            <span class="text-xs bg-amber-100 text-amber-600 px-2 py-0.5 rounded-full">放水0.3元</span>
                        </div>
                        <p class="text-sm mt-1 text-gray-700">老用户每日可提0.3元</p>
                        <div class="flex justify-between mt-1">
                            <span class="text-xs text-gray-500">小米13</span>
                            <span class="text-xs text-gray-500">2025-05-10 09:12</span>
                        </div>
                        <div class="grid grid-cols-2 gap-2 mt-2">
                            <img src="https://placehold.co/200x150/FFD700/333333?text=APP%E6%8F%90%E7%8E%B0%E8%AE%B0%E5%BD%95" alt="APP提现记录" class="w-full h-auto rounded-lg">
                            <img src="https://placehold.co/200x150/90EE90/333333?text=%E5%BE%AE%E4%BF%A1%E5%88%B0%E8%B4%A6%E8%AE%B0%E5%BD%95" alt="微信到账记录" class="w-full h-auto rounded-lg">
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <button class="text-sm text-red-500 font-medium flex items-center justify-center mx-auto">
                        查看更多记录 <iconify-icon icon="mdi:chevron-down" class="ml-1"></iconify-icon>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 常见问题 -->
        <div class="card mt-5 p-4 mb-8">
            <div class="flex items-center">
                <iconify-icon icon="mdi:help-circle" class="text-xl text-green-500 mr-2"></iconify-icon>
                <h3 class="font-bold">常见问题</h3>
            </div>
            <div class="mt-3">
                <div class="py-2 border-b border-gray-100">
                    <h4 class="font-medium text-sm">如何让红包变大？</h4>
                    <p class="text-xs text-gray-600 mt-2">连续签到7天可获得更大红包，邀请好友也可增加红包额度。开通VIP会员后，所有红包额度提升50%。每天10-12点活动期间，红包金额翻倍。</p>
                </div>
                <div class="py-2">
                    <h4 class="font-medium text-sm">红包提现不到账如何避免？</h4>
                    <p class="text-xs text-gray-600 mt-2">建议使用实名账号，提现前完成身份验证。检查银行卡信息是否正确。部分平台首次提现可能有24小时审核期，耐心等待。若超过48小时未到账，可联系客服处理。</p>
                </div>
                <div class="text-center mt-3">
                    <button class="text-sm text-red-500 font-medium flex items-center justify-center mx-auto">
                        更多问题解答 <iconify-icon icon="mdi:chevron-right" class="ml-1"></iconify-icon>
                    </button>
                </div>
            </div>
        </div>

        <!-- 行动按钮 -->
        <div class="fixed-section bg-white border-t border-gray-200 py-4 px-5">
            <button class="primary-gradient-btn w-full py-3 font-bold shadow-lg">立即赚钱</button>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:home" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">评测</span>
            </a>
            <a href="#" class="flex flex-col items-center">
                <div class="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center -mt-5 shadow-lg">
                    <iconify-icon icon="mdi:water" class="text-2xl text-white"></iconify-icon>
                </div>
                <span class="text-xs mt-1 text-blue-600">线索</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-group-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">邀请</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
</div>

<!-- 放水线索页面 -->
<div class="app-container">
    <div class="fixed-section bg-white py-4 px-4 border-b border-gray-100">
        <div class="relative w-full">
            <input type="text" placeholder="搜索线索" class="w-full bg-gray-100 rounded-full py-2 px-4 text-sm focus:outline-none">
            <iconify-icon icon="mdi:magnify" class="absolute right-3 top-2 text-gray-500"></iconify-icon>
        </div>
        <div class="flex justify-between items-center mt-3">
            <div class="flex">
                <button class="font-bold mr-6 border-b-2 border-red-500 pb-2">最新线索</button>
                <button class="font-medium text-gray-500 mr-6 pb-2">高收益</button>
                <button class="font-medium text-gray-500 pb-2">热门</button>
            </div>
        </div>
    </div>

    <div class="content-flex hide-scrollbar px-4 pt-2">
        <!-- 提交按钮 -->
        <div class="sticky top-0 z-10 bg-white py-3">
            <button class="w-full bg-gradient-to-r from-red-600 to-red-700 text-white py-3 rounded-lg font-bold flex items-center justify-center">
                <iconify-icon icon="mdi:plus-circle" class="mr-2 text-xl"></iconify-icon>
                提交放水线索赚取积分
            </button>
        </div>
        
        <!-- 放水线索时间线 -->
        <div class="mt-4 relative">
            <!-- 放水线索1 -->
            <div class="flex mb-6">
                <div class="flex flex-col items-center mr-4">
                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center">
                        <iconify-icon icon="mdi:water" class="text-red-600 text-lg"></iconify-icon>
                    </div>
                    <div class="w-0.5 bg-gray-200 flex-grow mt-2"></div>
                </div>
                <div class="flex-1">
                    <div class="text-xs text-gray-500 mb-1">2025-05-13 10:23</div>
                    <div class="card p-4 bg-gradient-to-r from-red-50 to-white">
                        <div class="flex justify-between">
                            <div class="flex items-center">
                                <iconify-icon icon="mdi:coin-outline" class="text-xl text-amber-500 mr-2"></iconify-icon>
                                <span class="font-semibold">金银合合</span>
                            </div>
                            <div class="flex gap-4">
                                <span class="flex items-center text-amber-400">
                                    <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>4.7
                                </span>
                                <span class="flex items-center text-gray-600">
                                    <iconify-icon icon="mdi:download-outline" class="mr-1"></iconify-icon>2360次
                                </span>
                            </div>
                        </div>
                        <div class="flex gap-2 mt-3">
                            <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥自动</span>
                            <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡$0.1起提</span>
                            <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">新人$0.25</span>
                            <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">顶包￥2</span>
                        </div>
                        <div class="mt-3 bg-gray-50 p-3 rounded-lg text-sm">
                            <p>用户反馈：<span class="text-red-500 font-medium">今日放水1.2元</span>，新人秒到账，实名后提现速度非常快！</p>
                            <p class="mt-1 text-xs text-gray-500">提交人：用户8273 [华为mate40 Pro]</p>
                        </div>
                        <div class="flex justify-between items-center mt-3">
                            <button class="red-gradient-btn px-3 py-1 text-sm">下载赚钱</button>
                            <button class="text-red-500 font-semibold text-sm flex items-center">
                                查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 放水线索2 -->
            <div class="flex mb-6">
                <div class="flex flex-col items-center mr-4">
                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center">
                        <iconify-icon icon="mdi:water" class="text-red-600 text-lg"></iconify-icon>
                    </div>
                    <div class="w-0.5 bg-gray-200 flex-grow mt-2"></div>
                </div>
                <div class="flex-1">
                    <div class="text-xs text-gray-500 mb-1">2025-05-12 18:45</div>
                    <div class="card p-4 bg-gradient-to-r from-blue-50 to-white">
                        <div class="flex justify-between">
                            <div class="flex items-center">
                                <iconify-icon icon="mdi:cash-multiple" class="text-xl text-green-500 mr-2"></iconify-icon>
                                <span class="font-semibold">趣步多多</span>
                            </div>
                            <div class="flex gap-4">
                                <span class="flex items-center text-amber-400">
                                    <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>4.5
                                </span>
                                <span class="flex items-center text-gray-600">
                                    <iconify-icon icon="mdi:download-outline" class="mr-1"></iconify-icon>1860次
                                </span>
                            </div>
                        </div>
                        <div class="flex gap-2 mt-3">
                            <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥高佣</span>
                            <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡$0.5起提</span>
                            <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">走路赚钱</span>
                            <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">首单￥3</span>
                        </div>
                        <div class="mt-3 bg-gray-50 p-3 rounded-lg text-sm">
                            <p>用户反馈：<span class="text-red-500 font-medium">放水0.8元</span>，到账速度快，今天特别活动双倍奖励！</p>
                            <p class="mt-1 text-xs text-gray-500">提交人：用户5146 [小米13]</p>
                        </div>
                        <div class="flex justify-between items-center mt-3">
                            <button class="red-gradient-btn px-3 py-1 text-sm">下载赚钱</button>
                            <button class="text-red-500 font-semibold text-sm flex items-center">
                                查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 放水线索3 -->
            <div class="flex mb-6">
                <div class="flex flex-col items-center mr-4">
                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center">
                        <iconify-icon icon="mdi:water" class="text-red-600 text-lg"></iconify-icon>
                    </div>
                    <div class="w-0.5 bg-gray-200 flex-grow mt-2"></div>
                </div>
                <div class="flex-1">
                    <div class="text-xs text-gray-500 mb-1">2025-05-12 15:10</div>
                    <div class="card p-4 bg-gradient-to-r from-amber-50 to-white">
                        <div class="flex justify-between">
                            <div class="flex items-center">
                                <iconify-icon icon="mdi:video" class="text-xl text-red-500 mr-2"></iconify-icon>
                                <span class="font-semibold">短剧星球</span>
                            </div>
                            <div class="flex gap-4">
                                <span class="flex items-center text-amber-400">
                                    <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>4.9
                                </span>
                                <span class="flex items-center text-gray-600">
                                    <iconify-icon icon="mdi:download-outline" class="mr-1"></iconify-icon>3560次
                                </span>
                            </div>
                        </div>
                        <div class="flex gap-2 mt-3">
                            <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥爆款</span>
                            <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡秒提现</span>
                            <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">新人$1.0</span>
                            <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">首充返￥10</span>
                        </div>
                        <div class="mt-3 bg-gray-50 p-3 rounded-lg text-sm">
                            <p>用户反馈：<span class="text-red-500 font-medium">今日放水1.5元</span>，看剧10分钟就能提现，非常良心！</p>
                            <p class="mt-1 text-xs text-gray-500">提交人：用户7621 [OPPO Find X5]</p>
                        </div>
                        <div class="flex justify-between items-center mt-3">
                            <button class="red-gradient-btn px-3 py-1 text-sm">下载赚钱</button>
                            <button class="text-red-500 font-semibold text-sm flex items-center">
                                查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 放水线索4 -->
            <div class="flex mb-10">
                <div class="flex flex-col items-center mr-4">
                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center">
                        <iconify-icon icon="mdi:water" class="text-red-600 text-lg"></iconify-icon>
                    </div>
                </div>
                <div class="flex-1">
                    <div class="text-xs text-gray-500 mb-1">2025-05-11 09:33</div>
                    <div class="card p-4 bg-gradient-to-r from-green-50 to-white">
                        <div class="flex justify-between">
                            <div class="flex items-center">
                                <iconify-icon icon="mdi:book-open-variant" class="text-xl text-blue-500 mr-2"></iconify-icon>
                                <span class="font-semibold">阅读赚</span>
                            </div>
                            <div class="flex gap-4">
                                <span class="flex items-center text-amber-400">
                                    <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>4.6
                                </span>
                                <span class="flex items-center text-gray-600">
                                    <iconify-icon icon="mdi:download-outline" class="mr-1"></iconify-icon>2120次
                                </span>
                            </div>
                        </div>
                        <div class="flex gap-2 mt-3">
                            <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥稳定</span>
                            <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡$1起提</span>
                            <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">新人$0.5</span>
                            <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">签到￥0.5</span>
                        </div>
                        <div class="mt-3 bg-gray-50 p-3 rounded-lg text-sm">
                            <p>用户反馈：<span class="text-red-500 font-medium">连续签到奖励0.6元</span>，今天阅读文章特别多，收益高！</p>
                            <p class="mt-1 text-xs text-gray-500">提交人：用户1934 [vivo X90]</p>
                        </div>
                        <div class="flex justify-between items-center mt-3">
                            <button class="red-gradient-btn px-3 py-1 text-sm">下载赚钱</button>
                            <button class="text-red-500 font-semibold text-sm flex items-center">
                                查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 加载更多 -->
            <div class="text-center mb-8">
                <button class="text-sm text-red-500 font-medium flex items-center justify-center mx-auto">
                    加载更多 <iconify-icon icon="mdi:chevron-down" class="ml-1"></iconify-icon>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:home" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-blue-600">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">评测</span>
            </a>
            <a href="#" class="flex flex-col items-center">
                <div class="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center -mt-5 shadow-lg">
                    <iconify-icon icon="mdi:water" class="text-2xl text-white"></iconify-icon>
                </div>
                <span class="text-xs mt-1 text-gray-500">线索</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-group-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">邀请</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
</div>

<!-- 提交评测报告页面 -->
<div class="app-container">
    <div class="fixed-section bg-white py-3 px-4 border-b border-gray-100">
        <div class="flex items-center">
            <iconify-icon icon="mdi:arrow-left" class="text-2xl text-gray-700"></iconify-icon>
            <h1 class="text-lg font-bold ml-4">提交评测报告</h1>
        </div>
    </div>

    <div class="content-flex hide-scrollbar px-4 pt-4">
        <form class="space-y-5 pb-8">
            <!-- APP Logo上传 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">上传APP Logo</label>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:bg-gray-50 cursor-pointer">
                    <iconify-icon icon="mdi:cloud-upload" class="text-4xl text-gray-400 mx-auto"></iconify-icon>
                    <p class="mt-2 text-sm text-gray-500">点击上传APP Logo</p>
                    <p class="text-xs text-gray-400 mt-1">支持JPG、PNG格式，尺寸建议512x512</p>
                </div>
            </div>
            
            <!-- APP名称 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">APP名称</label>
                <input type="text" placeholder="请输入APP的完整名称" class="block w-full bg-white border border-gray-300 rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
            </div>
            
            <!-- 打分 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">评分（1-5分）</label>
                <div class="flex items-center space-x-2">
                    <iconify-icon icon="mdi:star" class="text-2xl text-amber-400"></iconify-icon>
                    <iconify-icon icon="mdi:star" class="text-2xl text-amber-400"></iconify-icon>
                    <iconify-icon icon="mdi:star" class="text-2xl text-amber-400"></iconify-icon>
                    <iconify-icon icon="mdi:star" class="text-2xl text-amber-400"></iconify-icon>
                    <iconify-icon icon="mdi:star-outline" class="text-2xl text-gray-300"></iconify-icon>
                </div>
                <p class="text-xs text-gray-500 mt-1">点击星星进行评分</p>
            </div>
            
            <!-- 游戏类型 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">APP类型</label>
                <div class="grid grid-cols-3 gap-3">
                    <label class="relative flex items-center justify-center">
                        <input type="radio" name="app-type" class="absolute opacity-0 w-full h-full cursor-pointer" value="合成游戏">
                        <div class="border border-gray-300 rounded-lg py-2 text-center w-full cursor-pointer hover:bg-gray-50">
                            合成游戏
                        </div>
                    </label>
                    <label class="relative flex items-center justify-center">
                        <input type="radio" name="app-type" class="absolute opacity-0 w-full h-full cursor-pointer" value="短剧">
                        <div class="border border-gray-300 rounded-lg py-2 text-center w-full cursor-pointer hover:bg-gray-50">
                            短剧
                        </div>
                    </label>
                    <label class="relative flex items-center justify-center">
                        <input type="radio" name="app-type" class="absolute opacity-0 w-full h-full cursor-pointer" value="阅读">
                        <div class="border border-gray-300 rounded-lg py-2 text-center w-full cursor-pointer hover:bg-gray-50">
                            阅读
                        </div>
                    </label>
                    <label class="relative flex items-center justify-center">
                        <input type="radio" name="app-type" class="absolute opacity-0 w-full h-full cursor-pointer" value="走路">
                        <div class="border border-gray-300 rounded-lg py-2 text-center w-full cursor-pointer hover:bg-gray-50">
                            走路
                        </div>
                    </label>
                    <label class="relative flex items-center justify-center">
                        <input type="radio" name="app-type" class="absolute opacity-0 w-full h-full cursor-pointer" value="答题">
                        <div class="border border-gray-300 rounded-lg py-2 text-center w-full cursor-pointer hover:bg-gray-50">
                            答题
                        </div>
                    </label>
                    <label class="relative flex items-center justify-center">
                        <input type="radio" name="app-type" class="absolute opacity-0 w-full h-full cursor-pointer" value="其他">
                        <div class="border border-gray-300 rounded-lg py-2 text-center w-full cursor-pointer hover:bg-gray-50">
                            其他
                        </div>
                    </label>
                </div>
            </div>
            
            <!-- 运行模式 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">运行模式</label>
                <div class="grid grid-cols-2 gap-3">
                    <label class="relative flex items-center justify-center">
                        <input type="radio" name="app-mode" class="absolute opacity-0 w-full h-full cursor-pointer" value="自动">
                        <div class="border border-gray-300 rounded-lg py-2 text-center w-full cursor-pointer hover:bg-gray-50">
                            <iconify-icon icon="mdi:robot" class="mr-1 text-blue-500"></iconify-icon> 自动
                        </div>
                    </label>
                    <label class="relative flex items-center justify-center">
                        <input type="radio" name="app-mode" class="absolute opacity-0 w-full h-full cursor-pointer" value="手动">
                        <div class="border border-gray-300 rounded-lg py-2 text-center w-full cursor-pointer hover:bg-gray-50">
                            <iconify-icon icon="mdi:hand" class="mr-1 text-amber-500"></iconify-icon> 手动
                        </div>
                    </label>
                </div>
            </div>
            
            <!-- 新人福利 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">新人福利(元)</label>
                <div class="relative">
                    <span class="absolute left-4 top-3 text-gray-500">￥</span>
                    <input type="number" step="0.01" placeholder="0.00" class="block w-full bg-white border border-gray-300 rounded-lg py-3 pl-8 pr-4 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                </div>
            </div>
            
            <!-- 提现门槛 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">提现门槛(元)</label>
                <div class="relative">
                    <span class="absolute left-4 top-3 text-gray-500">￥</span>
                    <input type="number" step="0.01" placeholder="0.00" class="block w-full bg-white border border-gray-300 rounded-lg py-3 pl-8 pr-4 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                </div>
            </div>
            
            <!-- 顶包金额 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">顶包金额(元)</label>
                <div class="relative">
                    <span class="absolute left-4 top-3 text-gray-500">￥</span>
                    <input type="number" step="0.01" placeholder="0.00" class="block w-full bg-white border border-gray-300 rounded-lg py-3 pl-8 pr-4 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                </div>
            </div>
            
            <!-- 测试条数 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">测试条数</label>
                <div class="grid grid-cols-2 gap-3">
                    <label class="relative flex items-center justify-center">
                        <input type="radio" name="test-count" class="absolute opacity-0 w-full h-full cursor-pointer" value="5">
                        <div class="border border-gray-300 rounded-lg py-2 text-center w-full cursor-pointer hover:bg-gray-50">
                            5条
                        </div>
                    </label>
                    <label class="relative flex items-center justify-center">
                        <input type="radio" name="test-count" class="absolute opacity-0 w-full h-full cursor-pointer" value="10">
                        <div class="border border-gray-300 rounded-lg py-2 text-center w-full cursor-pointer hover:bg-gray-50">
                            10条
                        </div>
                    </label>
                </div>
            </div>
            
            <!-- 测试收益 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">测试总收益(元)</label>
                <div class="relative">
                    <span class="absolute left-4 top-3 text-gray-500">￥</span>
                    <input type="number" step="0.01" placeholder="0.00" class="block w-full bg-white border border-gray-300 rounded-lg py-3 pl-8 pr-4 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                </div>
            </div>
            
            <!-- 测试时长 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">测试时长(分钟)</label>
                <input type="number" placeholder="如：10" class="block w-full bg-white border border-gray-300 rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
            </div>
            
            <!-- 测试设备 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">测试设备</label>
                <input type="text" placeholder="如：iPhone 15 Pro / 华为mate40" class="block w-full bg-white border border-gray-300 rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
            </div>
            
            <!-- 测评人和测试时间 -->
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">测评人</label>
                    <input type="text" placeholder="输入测评人姓名" class="block w-full bg-white border border-gray-300 rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">测试日期</label>
                    <input type="date" class="block w-full bg-white border border-gray-300 rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                </div>
            </div>
            
            <!-- 每条广告价格 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">各条收益明细（用于生成曲线图）</label>
                <div class="space-y-3">
                    <div id="price-inputs">
                        <div class="flex items-center space-x-2 mb-2">
                            <input type="number" step="0.01" placeholder="第1条价格" class="block w-full bg-white border border-gray-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            <input type="number" step="0.01" placeholder="第2条价格" class="block w-full bg-white border border-gray-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                        </div>
                        <div class="flex items-center space-x-2 mb-2">
                            <input type="number" step="0.01" placeholder="第3条价格" class="block w-full bg-white border border-gray-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            <input type="number" step="0.01" placeholder="第4条价格" class="block w-full bg-white border border-gray-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                        </div>
                        <div class="flex items-center space-x-2 mb-2">
                            <input type="number" step="0.01" placeholder="第5条价格" class="block w-full bg-white border border-gray-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            <input type="number" step="0.01" placeholder="第6条价格" class="block w-full bg-white border border-gray-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                        </div>
                        <div class="flex items-center space-x-2 mb-2">
                            <input type="number" step="0.01" placeholder="第7条价格" class="block w-full bg-white border border-gray-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            <input type="number" step="0.01" placeholder="第8条价格" class="block w-full bg-white border border-gray-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                        </div>
                        <div class="flex items-center space-x-2 mb-2">
                            <input type="number" step="0.01" placeholder="第9条价格" class="block w-full bg-white border border-gray-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            <input type="number" step="0.01" placeholder="第10条价格" class="block w-full bg-white border border-gray-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 游戏主界面截图上传 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">游戏主界面截图</label>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:bg-gray-50 cursor-pointer">
                    <iconify-icon icon="mdi:cloud-upload" class="text-4xl text-gray-400 mx-auto"></iconify-icon>
                    <p class="mt-2 text-sm text-gray-500">点击上传游戏主界面截图</p>
                    <p class="text-xs text-gray-400 mt-1">支持JPG、PNG格式</p>
                </div>
            </div>
            
            <!-- APP内提现记录界面上传 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">APP内提现记录截图</label>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:bg-gray-50 cursor-pointer">
                    <iconify-icon icon="mdi:cloud-upload" class="text-4xl text-gray-400 mx-auto"></iconify-icon>
                    <p class="mt-2 text-sm text-gray-500">点击上传APP内提现记录截图</p>
                    <p class="text-xs text-gray-400 mt-1">支持JPG、PNG格式</p>
                </div>
            </div>
            
            <!-- 微信到账记录上传 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">微信到账记录截图</label>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:bg-gray-50 cursor-pointer">
                    <iconify-icon icon="mdi:cloud-upload" class="text-4xl text-gray-400 mx-auto"></iconify-icon>
                    <p class="mt-2 text-sm text-gray-500">点击上传微信到账记录截图</p>
                    <p class="text-xs text-gray-400 mt-1">支持JPG、PNG格式</p>
                </div>
            </div>
            
            <!-- 测评报告 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">测评报告</label>
                <textarea rows="5" placeholder="详细描述测评情况，包括使用体验、收益情况、提现速度等..." class="block w-full bg-white border border-gray-300 rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"></textarea>
            </div>
            
            <!-- 提交按钮 -->
            <div class="pt-4">
                <button type="submit" class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 rounded-lg font-bold">
                    提交评测报告（+50积分）
                </button>
                <p class="text-center text-xs text-gray-500 mt-2">提交并审核通过后将获得50积分奖励</p>
            </div>
        </form>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:home" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-blue-600">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">评测</span>
            </a>
            <a href="#" class="flex flex-col items-center">
                <div class="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center -mt-5 shadow-lg">
                    <iconify-icon icon="mdi:water" class="text-2xl text-white"></iconify-icon>
                </div>
                <span class="text-xs mt-1 text-gray-500">线索</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-group-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">邀请</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
</div>

<!-- 提交放水线索页面 -->
<div class="app-container">
    <div class="fixed-section bg-white py-3 px-4 border-b border-gray-100">
        <div class="flex items-center">
            <iconify-icon icon="mdi:arrow-left" class="text-2xl text-gray-700"></iconify-icon>
            <h1 class="text-lg font-bold ml-4">提交放水线索</h1>
        </div>
    </div>

    <div class="content-flex hide-scrollbar px-4 pt-4">
        <form class="space-y-5 pb-8">
            <!-- APP选择 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">选择APP</label>
                <div class="relative">
                    <select class="block w-full bg-white border border-gray-300 rounded-lg py-3 px-4 pr-8 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                        <option>金银合合</option>
                        <option>趣步多多</option>
                        <option>短剧星球</option>
                        <option>阅读赚</option>
                        <option>游戏红包</option>
                    </select>
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                        <iconify-icon icon="mdi:chevron-down"></iconify-icon>
                    </div>
                </div>
            </div>
            
            <!-- 放水时间 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">放水时间</label>
                <div class="relative">
                    <input type="datetime-local" class="block w-full bg-white border border-gray-300 rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                </div>
                <p class="text-xs text-gray-500 mt-1">默认为当前提交时间</p>
            </div>
            
            <!-- 单包大小 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">单包大小(元)</label>
                <div class="grid grid-cols-4 gap-3">
                    <label class="relative flex items-center justify-center">
                        <input type="radio" name="package-size" class="absolute opacity-0 w-full h-full cursor-pointer" value="0.1-0.29">
                        <div class="border border-gray-300 rounded-lg py-2 text-center w-full cursor-pointer hover:bg-gray-50 peer-checked:border-red-500 peer-checked:bg-red-50">
                            0.1-0.29
                        </div>
                    </label>
                    <label class="relative flex items-center justify-center">
                        <input type="radio" name="package-size" class="absolute opacity-0 w-full h-full cursor-pointer" value="0.3-0.49">
                        <div class="border border-gray-300 rounded-lg py-2 text-center w-full cursor-pointer hover:bg-gray-50 peer-checked:border-red-500 peer-checked:bg-red-50">
                            0.3-0.49
                        </div>
                    </label>
                    <label class="relative flex items-center justify-center">
                        <input type="radio" name="package-size" class="absolute opacity-0 w-full h-full cursor-pointer" value="0.5-0.99">
                        <div class="border border-gray-300 rounded-lg py-2 text-center w-full cursor-pointer hover:bg-gray-50 peer-checked:border-red-500 peer-checked:bg-red-50">
                            0.5-0.99
                        </div>
                    </label>
                    <label class="relative flex items-center justify-center">
                        <input type="radio" name="package-size" class="absolute opacity-0 w-full h-full cursor-pointer" value="1以上">
                        <div class="border border-gray-300 rounded-lg py-2 text-center w-full cursor-pointer hover:bg-gray-50 peer-checked:border-red-500 peer-checked:bg-red-50">
                            1以上
                        </div>
                    </label>
                </div>
            </div>
            
            <!-- 设备型号 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">设备型号</label>
                <input type="text" placeholder="如：iPhone 15 Pro / 华为mate40" class="block w-full bg-white border border-gray-300 rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
            </div>
            
            <!-- APP提现记录上传 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">APP提现记录截图</label>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:bg-gray-50 cursor-pointer">
                    <iconify-icon icon="mdi:cloud-upload" class="text-4xl text-gray-400 mx-auto"></iconify-icon>
                    <p class="mt-2 text-sm text-gray-500">点击上传图片</p>
                    <p class="text-xs text-gray-400 mt-1">支持JPG、PNG格式</p>
                </div>
            </div>
            
            <!-- 微信到账记录上传 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">微信到账记录截图</label>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:bg-gray-50 cursor-pointer">
                    <iconify-icon icon="mdi:cloud-upload" class="text-4xl text-gray-400 mx-auto"></iconify-icon>
                    <p class="mt-2 text-sm text-gray-500">点击上传图片</p>
                    <p class="text-xs text-gray-400 mt-1">支持JPG、PNG格式</p>
                </div>
            </div>
            
            <!-- 放水评语 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">放水评语</label>
                <textarea rows="4" placeholder="详细描述放水情况，如何操作获得更高收益等..." class="block w-full bg-white border border-gray-300 rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"></textarea>
            </div>
            
            <!-- 提交按钮 -->
            <div class="pt-4">
                <button type="submit" class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 rounded-lg font-bold">
                    提交线索（+10积分）
                </button>
                <p class="text-center text-xs text-gray-500 mt-2">提交并审核通过后将获得10积分奖励</p>
            </div>
        </form>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:home" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">评测</span>
            </a>
            <a href="#" class="flex flex-col items-center">
                <div class="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center -mt-5 shadow-lg">
                    <iconify-icon icon="mdi:water" class="text-2xl text-white"></iconify-icon>
                </div>
                <span class="text-xs mt-1 text-blue-600">线索</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-group-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">邀请</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
</div>

<!-- 手机购买页面 -->
<div class="app-container">
    <div class="fixed-section bg-white py-3 px-4 border-b border-gray-100">
        <div class="flex items-center">
            <iconify-icon icon="mdi:arrow-left" class="text-2xl text-gray-700 back-btn"></iconify-icon>
            <h1 class="text-lg font-bold ml-4">积分商城</h1>
        </div>
    </div>

    <div class="content-flex hide-scrollbar px-4 pt-4">
        <!-- 积分余额 -->
        <div class="bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg p-4 mb-4 text-white">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-sm">我的积分</p>
                    <p class="text-2xl font-bold">1250</p>
                </div>
                <button class="bg-white text-amber-600 px-3 py-1 rounded-lg text-sm font-medium" id="points-detail-btn">
                    积分明细
                </button>
            </div>
        </div>
        
        <!-- 筛选栏 -->
        <div class="flex mb-4 overflow-x-auto hide-scrollbar">
            <button class="whitespace-nowrap px-4 py-2 bg-blue-600 text-white rounded-full mr-2">全部</button>
            <button class="whitespace-nowrap px-4 py-2 bg-gray-100 rounded-full mr-2">手机</button>
            <button class="whitespace-nowrap px-4 py-2 bg-gray-100 rounded-full mr-2">VIP兑换码</button>
            <button class="whitespace-nowrap px-4 py-2 bg-gray-100 rounded-full mr-2">礼品卡</button>
            <button class="whitespace-nowrap px-4 py-2 bg-gray-100 rounded-full mr-2">实用工具</button>
        </div>

        <!-- 手机列表 -->
        <div class="grid grid-cols-2 gap-4 pb-8">
            <!-- 华为Mate40 Pro -->
            <div class="card overflow-hidden">
                <img src="https://placehold.co/400x400/e9ecef/495057?text=%E5%8D%8E%E4%B8%BAMate40+Pro" alt="华为Mate40 Pro" class="w-full h-36 object-cover">
                <div class="p-3">
                    <h3 class="font-medium text-sm">华为Mate40 Pro</h3>
                    <div class="mt-1 text-xs text-gray-600">适用于98%的评测APP</div>
                    <div class="flex justify-between items-center mt-2">
                        <div>
                            <span class="text-red-600 font-bold">￥3999</span>
                            <span class="text-xs text-gray-500">或</span>
                            <span class="text-amber-500 font-bold">1000积分+￥2999</span>
                        </div>
                        <button class="text-xs bg-blue-600 text-white px-2 py-1 rounded product-detail-btn" data-product-id="1">立即兑换</button>
                    </div>
                </div>
            </div>

            <!-- iPhone 15 Pro -->
            <div class="card overflow-hidden">
                <img src="https://placehold.co/400x400/e9ecef/495057?text=iPhone+15+Pro" alt="iPhone 15 Pro" class="w-full h-36 object-cover">
                <div class="p-3">
                    <h3 class="font-medium text-sm">iPhone 15 Pro</h3>
                    <div class="mt-1 text-xs text-gray-600">适用于95%的评测APP</div>
                    <div class="flex justify-between items-center mt-2">
                        <div>
                            <span class="text-red-600 font-bold">￥7999</span>
                            <span class="text-xs text-gray-500">或</span>
                            <span class="text-amber-500 font-bold">2000积分+￥5999</span>
                        </div>
                        <button class="text-xs bg-blue-600 text-white px-2 py-1 rounded product-detail-btn" data-product-id="2">立即兑换</button>
                    </div>
                </div>
            </div>

            <!-- 小米13 -->
            <div class="card overflow-hidden">
                <img src="https://placehold.co/400x400/e9ecef/495057?text=%E5%B0%8F%E7%B1%B313" alt="小米13" class="w-full h-36 object-cover">
                <div class="p-3">
                    <h3 class="font-medium text-sm">小米13</h3>
                    <div class="mt-1 text-xs text-gray-600">适用于90%的评测APP</div>
                    <div class="flex justify-between items-center mt-2">
                        <span class="text-red-600 font-bold">￥3499</span>
                        <button class="text-xs bg-blue-600 text-white px-2 py-1 rounded product-detail-btn" data-product-id="3">立即购买</button>
                    </div>
                </div>
            </div>

            <!-- OPPO Find X5 -->
            <div class="card overflow-hidden">
                <img src="https://placehold.co/400x400/e9ecef/495057?text=OPPO+Find+X5" alt="OPPO Find X5" class="w-full h-36 object-cover">
                <div class="p-3">
                    <h3 class="font-medium text-sm">OPPO Find X5</h3>
                    <div class="mt-1 text-xs text-gray-600">适用于88%的评测APP</div>
                    <div class="flex justify-between items-center mt-2">
                        <span class="text-red-600 font-bold">￥3299</span>
                        <button class="text-xs bg-blue-600 text-white px-2 py-1 rounded product-detail-btn" data-product-id="4">立即购买</button>
                    </div>
                </div>
            </div>

            <!-- vivo X90 -->
            <div class="card overflow-hidden">
                <img src="https://placehold.co/400x400/e9ecef/495057?text=vivo+X90" alt="vivo X90" class="w-full h-36 object-cover">
                <div class="p-3">
                    <h3 class="font-medium text-sm">vivo X90</h3>
                    <div class="mt-1 text-xs text-gray-600">适用于85%的评测APP</div>
                    <div class="flex justify-between items-center mt-2">
                        <span class="text-red-600 font-bold">￥3199</span>
                        <button class="text-xs bg-blue-600 text-white px-2 py-1 rounded product-detail-btn" data-product-id="5">立即购买</button>
                    </div>
                </div>
            </div>

            <!-- 华为Mate30 5G -->
            <div class="card overflow-hidden">
                <img src="https://placehold.co/400x400/e9ecef/495057?text=%E5%8D%8E%E4%B8%BAMate30+5G" alt="华为Mate30 5G" class="w-full h-36 object-cover">
                <div class="p-3">
                    <h3 class="font-medium text-sm">华为Mate30 5G</h3>
                    <div class="mt-1 text-xs text-gray-600">适用于80%的评测APP</div>
                    <div class="flex justify-between items-center mt-2">
                        <span class="text-red-600 font-bold">￥2999</span>
                        <button class="text-xs bg-blue-600 text-white px-2 py-1 rounded product-detail-btn" data-product-id="6">立即购买</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-blue-600">
                <iconify-icon icon="mdi:home" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">评测</span>
            </a>
            <a href="#" class="flex flex-col items-center">
                <div class="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center -mt-5 shadow-lg">
                    <iconify-icon icon="mdi:water" class="text-2xl text-white"></iconify-icon>
                </div>
                <span class="text-xs mt-1 text-gray-500">线索</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-group-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">邀请</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
</div>

<!-- VIP会员支付页面 -->
<div class="app-container" id="vip-payment-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 9999; align-items: center; justify-content: center;">
    <div class="bg-white rounded-lg w-5/6 max-w-md overflow-hidden">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="font-bold text-lg">VIP会员购买</h3>
            <button class="text-gray-500 close-modal">
                <iconify-icon icon="mdi:close" class="text-xl"></iconify-icon>
            </button>
        </div>
        
        <div class="p-4">
            <div class="bg-gradient-to-r from-amber-500 to-amber-600 p-4 rounded-lg text-white">
                <h4 class="font-bold text-center">VIP会员特权</h4>
                <ul class="mt-3 space-y-2">
                    <li class="flex items-center">
                        <iconify-icon icon="mdi:check-circle" class="mr-2"></iconify-icon>
                        解锁全量评测
                    </li>
                    <li class="flex items-center">
                        <iconify-icon icon="mdi:check-circle" class="mr-2"></iconify-icon>
                        每日放水线索
                    </li>
                    <li class="flex items-center">
                        <iconify-icon icon="mdi:check-circle" class="mr-2"></iconify-icon>
                        邀请立赚500元
                    </li>
                    <li class="flex items-center">
                        <iconify-icon icon="mdi:check-circle" class="mr-2"></iconify-icon>
                        专属客服服务
                    </li>
                </ul>
                <div class="text-center mt-4">
                    <div class="text-sm">原价: <span class="line-through">¥5980</span></div>
                    <div class="text-2xl font-bold mt-1">特惠: ¥3980/年</div>
                </div>
            </div>
            
            <div class="mt-4">
                <h4 class="font-medium mb-2">支付方式</h4>
                <div class="space-y-3">
                    <label class="flex items-center justify-between border p-3 rounded-lg">
                        <div class="flex items-center">
                            <input type="radio" name="payment" checked="" class="mr-3">
                            <iconify-icon icon="mdi:wechat" class="text-2xl text-green-500 mr-2"></iconify-icon>
                            微信支付
                        </div>
                        <iconify-icon icon="mdi:check-circle" class="text-green-500"></iconify-icon>
                    </label>
                    
                    <label class="flex items-center justify-between border p-3 rounded-lg">
                        <div class="flex items-center">
                            <input type="radio" name="payment" class="mr-3">
                            <iconify-icon icon="mdi:alipay" class="text-2xl text-blue-500 mr-2"></iconify-icon>
                            支付宝
                        </div>
                    </label>
                    
                    <label class="flex items-center justify-between border p-3 rounded-lg">
                        <div class="flex items-center">
                            <input type="radio" name="payment" class="mr-3">
                            <iconify-icon icon="mdi:ticket-percent" class="text-2xl text-red-500 mr-2"></iconify-icon>
                            兑换码兑换
                        </div>
                    </label>
                </div>
                
                <div id="redemption-code-input" class="mt-3" style="display: none;">
                    <input type="text" placeholder="请输入兑换码" class="block w-full bg-white border border-gray-300 rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                    <button class="w-full bg-gradient-to-r from-amber-500 to-amber-600 text-white py-2 rounded-lg font-bold mt-2">
                        验证兑换码
                    </button>
                </div>
                
                <button class="w-full bg-gradient-to-r from-red-600 to-red-700 text-white py-3 rounded-lg font-bold mt-6">
                    立即支付 ¥3980
                </button>
                
                <p class="text-xs text-center text-gray-500 mt-3">
                    点击立即支付，表示您同意<a href="#" class="text-blue-600">《会员服务协议》</a>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- 我的线索页面 -->
<div class="app-container">
    <div class="fixed-section bg-white py-3 px-4 border-b border-gray-100">
        <div class="flex items-center">
            <iconify-icon icon="mdi:arrow-left" class="text-2xl text-gray-700 back-to-profile"></iconify-icon>
            <h1 class="text-lg font-bold ml-4">我的线索</h1>
        </div>
    </div>

    <div class="content-flex hide-scrollbar px-4 pt-2">
        <!-- 筛选栏 -->
        <div class="flex justify-between items-center my-3">
            <div class="flex">
                <button class="font-bold mr-6 border-b-2 border-red-500 pb-2">全部线索</button>
                <button class="font-medium text-gray-500 mr-6 pb-2">审核通过</button>
                <button class="font-medium text-gray-500 pb-2">待审核</button>
            </div>
            <div class="ml-auto text-sm flex items-center text-gray-500">
                最新优先
                <iconify-icon icon="mdi:chevron-down"></iconify-icon>
            </div>
        </div>
        
        <!-- 线索列表 -->
        <div class="mt-3 space-y-4 pb-6">
            <!-- 线索卡片1 -->
            <div class="card p-4 bg-gradient-to-r from-red-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:coin-outline" class="text-xl text-amber-500 mr-2"></iconify-icon>
                        <span class="font-semibold">金银合合</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:check-circle" class="mr-1"></iconify-icon>审核通过
                        </span>
                        <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>+10分
                        </span>
                    </div>
                </div>
                <div class="mt-3 bg-gray-50 p-3 rounded-lg text-sm">
                    <p>用户反馈：<span class="text-red-500 font-medium">今日放水1.2元</span>，新人秒到账，实名后提现速度非常快！</p>
                </div>
                <div class="flex justify-between items-center mt-3">
                    <div class="text-xs text-gray-500">提交时间：2025-05-12 14:23</div>
                    <button class="text-blue-500 font-semibold text-sm flex items-center">
                        查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
            
            <!-- 线索卡片2 -->
            <div class="card p-4 bg-gradient-to-r from-blue-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:video" class="text-xl text-red-500 mr-2"></iconify-icon>
                        <span class="font-semibold">短剧星球</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:check-circle" class="mr-1"></iconify-icon>审核通过
                        </span>
                        <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>+10分
                        </span>
                    </div>
                </div>
                <div class="mt-3 bg-gray-50 p-3 rounded-lg text-sm">
                    <p>用户反馈：<span class="text-red-500 font-medium">放水1.5元</span>，看剧10分钟就能提现，非常良心！</p>
                </div>
                <div class="flex justify-between items-center mt-3">
                    <div class="text-xs text-gray-500">提交时间：2025-05-10 09:45</div>
                    <button class="text-blue-500 font-semibold text-sm flex items-center">
                        查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
            
            <!-- 线索卡片3 -->
            <div class="card p-4 bg-gradient-to-r from-amber-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:cash-multiple" class="text-xl text-green-500 mr-2"></iconify-icon>
                        <span class="font-semibold">趣步多多</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:clock" class="mr-1"></iconify-icon>审核中
                        </span>
                        <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>待确定
                        </span>
                    </div>
                </div>
                <div class="mt-3 bg-gray-50 p-3 rounded-lg text-sm">
                    <p>用户反馈：<span class="text-red-500 font-medium">放水0.8元</span>，到账速度快，今天特别活动双倍奖励！</p>
                </div>
                <div class="flex justify-between items-center mt-3">
                    <div class="text-xs text-gray-500">提交时间：2025-05-13 16:37</div>
                    <button class="text-blue-500 font-semibold text-sm flex items-center">
                        查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
            
            <!-- 线索卡片4 -->
            <div class="card p-4 bg-gradient-to-r from-green-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:book-open-variant" class="text-xl text-blue-500 mr-2"></iconify-icon>
                        <span class="font-semibold">阅读赚</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:close-circle" class="mr-1"></iconify-icon>未通过
                        </span>
                        <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>+0分
                        </span>
                    </div>
                </div>
                <div class="mt-3 bg-gray-50 p-3 rounded-lg text-sm">
                    <p>用户反馈：<span class="text-red-500 font-medium">连续签到奖励0.6元</span>，今天阅读文章特别多，收益高！</p>
                </div>
                <div class="flex justify-between items-center mt-3">
                    <div class="text-xs text-gray-500">提交时间：2025-05-08 11:20</div>
                    <div class="flex items-center">
                        <span class="text-red-500 text-xs mr-3">截图不清晰，请重新提交</span>
                        <button class="text-blue-500 font-semibold text-sm flex items-center">
                            查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 线索卡片5 -->
            <div class="card p-4 bg-gradient-to-r from-purple-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:gamepad-variant" class="text-xl text-purple-500 mr-2"></iconify-icon>
                        <span class="font-semibold">游戏红包</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:check-circle" class="mr-1"></iconify-icon>审核通过
                        </span>
                        <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>+10分
                        </span>
                    </div>
                </div>
                <div class="mt-3 bg-gray-50 p-3 rounded-lg text-sm">
                    <p>用户反馈：<span class="text-red-500 font-medium">首充返现1.0元</span>，秒到账，玩游戏同时还能赚钱！</p>
                </div>
                <div class="flex justify-between items-center mt-3">
                    <div class="text-xs text-gray-500">提交时间：2025-05-06 15:12</div>
                    <button class="text-blue-500 font-semibold text-sm flex items-center">
                        查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
            
            <!-- 加载更多 -->
            <div class="text-center mb-8">
                <button class="text-sm text-red-500 font-medium flex items-center justify-center mx-auto">
                    加载更多 <iconify-icon icon="mdi:chevron-down" class="ml-1"></iconify-icon>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:home" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">评测</span>
            </a>
            <a href="#" class="flex flex-col items-center">
                <div class="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center -mt-5 shadow-lg">
                    <iconify-icon icon="mdi:water" class="text-2xl text-white"></iconify-icon>
                </div>
                <span class="text-xs mt-1 text-blue-600">线索</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-group-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">邀请</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
</div>

<!-- 我的报告页面 -->
<div class="app-container">
    <div class="fixed-section bg-white py-3 px-4 border-b border-gray-100">
        <div class="flex items-center">
            <iconify-icon icon="mdi:arrow-left" class="text-2xl text-gray-700 back-to-profile"></iconify-icon>
            <h1 class="text-lg font-bold ml-4">我的报告</h1>
        </div>
    </div>

    <div class="content-flex hide-scrollbar px-4 pt-2">
        <!-- 筛选栏 -->
        <div class="flex justify-between items-center my-3">
            <div class="flex">
                <button class="font-bold mr-6 border-b-2 border-red-500 pb-2">全部报告</button>
                <button class="font-medium text-gray-500 mr-6 pb-2">审核通过</button>
                <button class="font-medium text-gray-500 pb-2">待审核</button>
            </div>
            <div class="ml-auto text-sm flex items-center text-gray-500">
                最新优先
                <iconify-icon icon="mdi:chevron-down"></iconify-icon>
            </div>
        </div>
        
        <!-- 报告列表 -->
        <div class="mt-3 space-y-4 pb-6">
            <!-- 报告卡片1 -->
            <div class="card p-4 bg-gradient-to-r from-green-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:coin-outline" class="text-xl text-amber-500 mr-2"></iconify-icon>
                        <span class="font-semibold">金币大师</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:check-circle" class="mr-1"></iconify-icon>审核通过
                        </span>
                        <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>+50分
                        </span>
                    </div>
                </div>
                <div class="flex gap-2 mt-3">
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥自动</span>
                    <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡$0.1起提</span>
                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">新人$0.25</span>
                </div>
                <div class="flex justify-between items-center mt-3">
                    <div class="text-xs text-gray-500">提交时间：2025-05-10 14:23</div>
                    <button class="text-blue-500 font-semibold text-sm flex items-center">
                        查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
            
            <!-- 报告卡片2 -->
            <div class="card p-4 bg-gradient-to-r from-blue-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:video" class="text-xl text-red-500 mr-2"></iconify-icon>
                        <span class="font-semibold">短剧星球</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:check-circle" class="mr-1"></iconify-icon>审核通过
                        </span>
                        <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>+50分
                        </span>
                    </div>
                </div>
                <div class="flex gap-2 mt-3">
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥爆款</span>
                    <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡秒提现</span>
                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">新人$1.0</span>
                </div>
                <div class="flex justify-between items-center mt-3">
                    <div class="text-xs text-gray-500">提交时间：2025-05-08 09:45</div>
                    <button class="text-blue-500 font-semibold text-sm flex items-center">
                        查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
            
            <!-- 报告卡片3 -->
            <div class="card p-4 bg-gradient-to-r from-amber-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:cash-multiple" class="text-xl text-green-500 mr-2"></iconify-icon>
                        <span class="font-semibold">趣步多多</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:clock" class="mr-1"></iconify-icon>审核中
                        </span>
                        <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>待确定
                        </span>
                    </div>
                </div>
                <div class="flex gap-2 mt-3">
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥高佣</span>
                    <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡$0.5起提</span>
                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">走路赚钱</span>
                </div>
                <div class="flex justify-between items-center mt-3">
                    <div class="text-xs text-gray-500">提交时间：2025-05-12 16:37</div>
                    <button class="text-blue-500 font-semibold text-sm flex items-center">
                        查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
            
            <!-- 报告卡片4 -->
            <div class="card p-4 bg-gradient-to-r from-purple-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:gamepad-variant" class="text-xl text-purple-500 mr-2"></iconify-icon>
                        <span class="font-semibold">游戏红包</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:close-circle" class="mr-1"></iconify-icon>未通过
                        </span>
                        <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>+0分
                        </span>
                    </div>
                </div>
                <div class="flex gap-2 mt-3">
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥有趣</span>
                    <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡$3起提</span>
                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">新人$0.8</span>
                </div>
                <div class="flex justify-between items-center mt-3">
                    <div class="text-xs text-gray-500">提交时间：2025-05-05 11:20</div>
                    <div class="flex items-center">
                        <span class="text-red-500 text-xs mr-3">截图不清晰，请重新提交</span>
                        <button class="text-blue-500 font-semibold text-sm flex items-center">
                            查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 报告卡片5 -->
            <div class="card p-4 bg-gradient-to-r from-green-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:book-open-variant" class="text-xl text-blue-500 mr-2"></iconify-icon>
                        <span class="font-semibold">阅读赚</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:check-circle" class="mr-1"></iconify-icon>审核通过
                        </span>
                        <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>+50分
                        </span>
                    </div>
                </div>
                <div class="flex gap-2 mt-3">
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥稳定</span>
                    <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡$1起提</span>
                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">新人$0.5</span>
                </div>
                <div class="flex justify-between items-center mt-3">
                    <div class="text-xs text-gray-500">提交时间：2025-04-30 15:12</div>
                    <button class="text-blue-500 font-semibold text-sm flex items-center">
                        查看详情 <iconify-icon icon="mdi:chevron-right" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
            
            <!-- 加载更多 -->
            <div class="text-center mb-8">
                <button class="text-sm text-red-500 font-medium flex items-center justify-center mx-auto">
                    加载更多 <iconify-icon icon="mdi:chevron-down" class="ml-1"></iconify-icon>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:home" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">评测</span>
            </a>
            <a href="#" class="flex flex-col items-center">
                <div class="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center -mt-5 shadow-lg">
                    <iconify-icon icon="mdi:water" class="text-2xl text-white"></iconify-icon>
                </div>
                <span class="text-xs mt-1 text-gray-500">线索</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-group-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">邀请</span>
            </a>
            <a href="#" class="flex flex-col items-center text-blue-600">
                <iconify-icon icon="mdi:account-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
</div>

<!-- 邀请页面 -->
<div class="app-container">
    <div class="fixed-section bg-white py-3 px-4 border-b border-gray-100">
        <div class="flex items-center">
            <h1 class="text-lg font-bold">邀请赚钱</h1>
            <div class="ml-auto">
                <button class="bg-gray-100 p-2 rounded-full">
                    <iconify-icon icon="mdi:help-circle-outline" class="text-xl"></iconify-icon>
                </button>
            </div>
        </div>
    </div>

    <div class="content-flex hide-scrollbar pt-2">
        <!-- 邀请码生成区 -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-800 p-6 text-white">
            <h2 class="text-center text-lg mb-4">您的专属邀请码</h2>
            <div class="text-center mb-5">
                <span class="text-4xl font-bold tracking-wider">VIP238</span>
                <div class="flex justify-center mt-2">
                    <button class="flex items-center text-xs bg-white/20 px-3 py-1 rounded-full">
                        <iconify-icon icon="mdi:content-copy" class="mr-1"></iconify-icon>
                        复制
                    </button>
                </div>
            </div>
            
            <div class="grid grid-cols-3 gap-3 mt-6">
                <button class="bg-green-600 py-2 rounded-lg flex flex-col items-center justify-center">
                    <iconify-icon icon="mdi:wechat" class="text-xl"></iconify-icon>
                    <span class="text-xs mt-1">微信</span>
                </button>
                <button class="bg-blue-500 py-2 rounded-lg flex flex-col items-center justify-center">
                    <iconify-icon icon="mdi:qqchat" class="text-xl"></iconify-icon>
                    <span class="text-xs mt-1">QQ</span>
                </button>
                <button class="bg-amber-500 py-2 rounded-lg flex flex-col items-center justify-center">
                    <iconify-icon icon="mdi:image" class="text-xl"></iconify-icon>
                    <span class="text-xs mt-1">生成海报</span>
                </button>
            </div>
            
            <div class="text-center mt-5 text-xs opacity-80">
                <p>每邀请1人加入，立得500元奖励</p>
                <p class="mt-1">邀请的用户充值，您将获得10%返现</p>
            </div>
        </div>

        <!-- 收益看板 -->
        <div class="px-4 py-5">
            <div class="bg-white rounded-lg p-4 shadow-sm mb-4">
                <div class="flex justify-between items-center">
                    <h3 class="font-bold">邀请收益</h3>
                    <a href="#" class="text-sm text-blue-500">收益明细</a>
                </div>
                <div class="mt-3 grid grid-cols-2 gap-4">
                    <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <span class="text-gray-500 text-xs">累计收益(元)</span>
                        <div class="text-2xl font-bold mt-1 text-blue-600 flex justify-center relative">
                            <span class="count-up" data-count="8630">8,630</span>
                        </div>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <span class="text-gray-500 text-xs">本月收益(元)</span>
                        <div class="text-2xl font-bold mt-1 text-red-500 flex justify-center relative">
                            <span class="count-up" data-count="1200">1,200</span>
                        </div>
                    </div>
                </div>
                
                <!-- 关系图谱 -->
                <div class="mt-5">
                    <div class="flex justify-between mb-2">
                        <span class="font-medium text-sm">关系图谱</span>
                        <span class="text-xs text-gray-500">共17人</span>
                    </div>
                    
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                            <iconify-icon icon="mdi:account" class="text-xl text-blue-600"></iconify-icon>
                        </div>
                        <div class="ml-2">
                            <span class="font-medium">您</span>
                        </div>
                    </div>
                    
                    <!-- 一级关系 -->
                    <div class="ml-5 mb-3">
                        <div class="border-l-2 border-dotted border-gray-300 pl-5 relative">
                            <span class="absolute -left-1 top-0 w-2 h-2 rounded-full bg-blue-500"></span>
                            <div class="font-medium text-sm mb-2">一级邀请 (12人)</div>
                            
                            <div class="flex flex-wrap gap-2">
                                <div class="flex items-center bg-gray-50 px-2 py-1 rounded">
                                    <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                                        <iconify-icon icon="mdi:account" class="text-sm text-blue-600"></iconify-icon>
                                    </div>
                                    <div class="ml-1 text-xs">
                                        <span>张三</span>
                                        <span class="text-green-500 ml-1">¥500</span>
                                    </div>
                                </div>
                                <div class="flex items-center bg-gray-50 px-2 py-1 rounded">
                                    <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                                        <iconify-icon icon="mdi:account" class="text-sm text-blue-600"></iconify-icon>
                                    </div>
                                    <div class="ml-1 text-xs">
                                        <span>李四</span>
                                        <span class="text-green-500 ml-1">¥500</span>
                                    </div>
                                </div>
                                <div class="flex items-center bg-gray-50 px-2 py-1 rounded">
                                    <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                                        <iconify-icon icon="mdi:account" class="text-sm text-blue-600"></iconify-icon>
                                    </div>
                                    <div class="ml-1 text-xs">
                                        <span>赵六</span>
                                        <span class="text-green-500 ml-1">¥500</span>
                                    </div>
                                </div>
                                <div class="flex items-center bg-gray-50 px-2 py-1 rounded">
                                    <div class="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center">
                                        <span class="text-xs">+9</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 二级关系 -->
                    <div class="ml-5">
                        <div class="border-l-2 border-dotted border-gray-300 pl-5 relative">
                            <span class="absolute -left-1 top-0 w-2 h-2 rounded-full bg-blue-500"></span>
                            <div class="font-medium text-sm mb-2">二级邀请 (5人)</div>
                            
                            <div class="flex flex-wrap gap-2">
                                <div class="flex items-center bg-gray-50 px-2 py-1 rounded">
                                    <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                                        <iconify-icon icon="mdi:account" class="text-sm text-blue-600"></iconify-icon>
                                    </div>
                                    <div class="ml-1 text-xs">
                                        <span>王五</span>
                                        <span class="text-green-500 ml-1">¥200</span>
                                    </div>
                                </div>
                                <div class="flex items-center bg-gray-50 px-2 py-1 rounded">
                                    <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                                        <iconify-icon icon="mdi:account" class="text-sm text-blue-600"></iconify-icon>
                                    </div>
                                    <div class="ml-1 text-xs">
                                        <span>钱七</span>
                                        <span class="text-green-500 ml-1">¥200</span>
                                    </div>
                                </div>
                                <div class="flex items-center bg-gray-50 px-2 py-1 rounded">
                                    <div class="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center">
                                        <span class="text-xs">+3</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 排行榜 -->
            <div class="bg-white rounded-lg p-4 shadow-sm mb-8">
                <h3 class="font-bold mb-4">邀请排行榜 <span class="text-xs text-gray-500 font-normal">（本月）</span></h3>
                <div class="space-y-3">
                    <!-- TOP 1 -->
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-8 h-8 flex items-center justify-center">
                            <iconify-icon icon="mdi:crown" class="text-amber-400 text-2xl"></iconify-icon>
                        </div>
                        <div class="ml-2 flex-1">
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <div class="w-7 h-7 rounded-full bg-blue-100 overflow-hidden flex items-center justify-center">
                                        <iconify-icon icon="mdi:account" class="text-lg text-blue-600"></iconify-icon>
                                    </div>
                                    <span class="font-medium ml-2">用户9527</span>
                                </div>
                                <div class="text-amber-500 font-semibold">¥9800</div>
                            </div>
                            <div class="w-full h-2 bg-gray-100 rounded-full mt-2 overflow-hidden">
                                <div class="h-full bg-amber-400 rounded-full" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- TOP 2 -->
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-8 h-8 flex items-center justify-center text-lg font-bold text-gray-500">
                            2
                        </div>
                        <div class="ml-2 flex-1">
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <div class="w-7 h-7 rounded-full bg-blue-100 overflow-hidden flex items-center justify-center">
                                        <iconify-icon icon="mdi:account" class="text-lg text-blue-600"></iconify-icon>
                                    </div>
                                    <span class="font-medium ml-2">用户8238</span>
                                </div>
                                <div class="text-gray-700 font-semibold">¥8900</div>
                            </div>
                            <div class="w-full h-2 bg-gray-100 rounded-full mt-2 overflow-hidden">
                                <div class="h-full bg-blue-400 rounded-full" style="width: 90%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- TOP 3 -->
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-8 h-8 flex items-center justify-center text-lg font-bold text-gray-500">
                            3
                        </div>
                        <div class="ml-2 flex-1">
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <div class="w-7 h-7 rounded-full bg-blue-100 overflow-hidden flex items-center justify-center">
                                        <iconify-icon icon="mdi:account" class="text-lg text-blue-600"></iconify-icon>
                                    </div>
                                    <span class="font-medium ml-2">用户3721</span>
                                </div>
                                <div class="text-gray-700 font-semibold">¥8630</div>
                            </div>
                            <div class="w-full h-2 bg-gray-100 rounded-full mt-2 overflow-hidden">
                                <div class="h-full bg-blue-400 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 您的排名 -->
                    <div class="flex items-center bg-blue-50 p-2 rounded">
                        <div class="flex-shrink-0 w-8 h-8 flex items-center justify-center text-lg font-bold text-blue-600">
                            4
                        </div>
                        <div class="ml-2 flex-1">
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <div class="w-7 h-7 rounded-full bg-blue-100 overflow-hidden flex items-center justify-center">
                                        <iconify-icon icon="mdi:account" class="text-lg text-blue-600"></iconify-icon>
                                    </div>
                                    <span class="font-medium ml-2">您</span>
                                    <span class="bg-blue-100 text-blue-600 text-xs px-1.5 py-0.5 rounded ml-2">当前</span>
                                </div>
                                <div class="text-blue-600 font-semibold">¥8630</div>
                            </div>
                            <div class="w-full h-2 bg-white rounded-full mt-2 overflow-hidden">
                                <div class="h-full bg-blue-500 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- TOP 5-10 -->
                    <div class="space-y-2 mt-2">
                        <div class="flex justify-between items-center px-2 py-1.5 hover:bg-gray-50 rounded">
                            <div class="flex items-center">
                                <div class="w-5 h-5 flex items-center justify-center text-sm font-medium text-gray-500">
                                    5
                                </div>
                                <div class="w-6 h-6 rounded-full bg-blue-100 overflow-hidden flex items-center justify-center ml-2">
                                    <iconify-icon icon="mdi:account" class="text-md text-blue-600"></iconify-icon>
                                </div>
                                <span class="text-sm ml-2">用户6283</span>
                            </div>
                            <div class="text-sm text-gray-700">¥7500</div>
                        </div>
                        <div class="flex justify-between items-center px-2 py-1.5 hover:bg-gray-50 rounded">
                            <div class="flex items-center">
                                <div class="w-5 h-5 flex items-center justify-center text-sm font-medium text-gray-500">
                                    6
                                </div>
                                <div class="w-6 h-6 rounded-full bg-blue-100 overflow-hidden flex items-center justify-center ml-2">
                                    <iconify-icon icon="mdi:account" class="text-md text-blue-600"></iconify-icon>
                                </div>
                                <span class="text-sm ml-2">用户1892</span>
                            </div>
                            <div class="text-sm text-gray-700">¥7200</div>
                        </div>
                        <div class="flex justify-between items-center px-2 py-1.5 hover:bg-gray-50 rounded">
                            <div class="flex items-center">
                                <div class="w-5 h-5 flex items-center justify-center text-sm font-medium text-gray-500">
                                    7
                                </div>
                                <div class="w-6 h-6 rounded-full bg-blue-100 overflow-hidden flex items-center justify-center ml-2">
                                    <iconify-icon icon="mdi:account" class="text-md text-blue-600"></iconify-icon>
                                </div>
                                <span class="text-sm ml-2">用户7726</span>
                            </div>
                            <div class="text-sm text-gray-700">¥6800</div>
                        </div>
                    </div>
                </div>
                
                <button class="w-full text-center text-blue-500 mt-3 text-sm">
                    查看完整榜单 <iconify-icon icon="mdi:chevron-right"></iconify-icon>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:home" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">评测</span>
            </a>
            <a href="#" class="flex flex-col items-center">
                <div class="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center -mt-5 shadow-lg">
                    <iconify-icon icon="mdi:water" class="text-2xl text-white"></iconify-icon>
                </div>
                <span class="text-xs mt-1 text-gray-500">线索</span>
            </a>
            <a href="#" class="flex flex-col items-center text-blue-600">
                <iconify-icon icon="mdi:account-group-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">邀请</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
</div>

<!-- VIP购买页面 -->
<div class="app-container">
    <div class="fixed-section bg-white py-3 px-4 border-b border-gray-100">
        <div class="flex items-center">
            <iconify-icon icon="mdi:arrow-left" class="text-2xl text-gray-700 back-to-profile"></iconify-icon>
            <h1 class="text-lg font-bold ml-4">VIP会员购买</h1>
        </div>
    </div>

    <div class="content-flex hide-scrollbar px-4 pt-2">
        <!-- 会员权益展示 -->
        <div class="bg-gradient-to-r from-amber-500 to-amber-600 p-6 rounded-lg text-white mt-4">
            <h2 class="text-xl font-bold text-center">VIP会员特权</h2>
            <ul class="mt-5 space-y-3">
                <li class="flex items-center">
                    <iconify-icon icon="mdi:check-circle" class="mr-3 text-xl"></iconify-icon>
                    <span>解锁全量APP评测资源</span>
                </li>
                <li class="flex items-center">
                    <iconify-icon icon="mdi:check-circle" class="mr-3 text-xl"></iconify-icon>
                    <span>每日放水线索优先获取</span>
                </li>
                <li class="flex items-center">
                    <iconify-icon icon="mdi:check-circle" class="mr-3 text-xl"></iconify-icon>
                    <span>邀请好友立赚500元</span>
                </li>
                <li class="flex items-center">
                    <iconify-icon icon="mdi:check-circle" class="mr-3 text-xl"></iconify-icon>
                    <span>VIP专属客服服务</span>
                </li>
                <li class="flex items-center">
                    <iconify-icon icon="mdi:check-circle" class="mr-3 text-xl"></iconify-icon>
                    <span>高额任务优先推送</span>
                </li>
            </ul>

            <div class="mt-5 text-center">
                <div class="text-sm">原价: <span class="line-through">￥5980/年</span></div>
                <div class="text-3xl font-bold mt-2">特惠价: ￥3980/年</div>
                <div class="text-sm mt-2">限时优惠，仅剩 <span id="countdown">23:59:59</span></div>
            </div>
        </div>

        <!-- 购买方式 -->
        <div class="mt-6">
            <div class="bg-white rounded-lg p-4 shadow-sm">
                <h3 class="font-bold mb-4">选择购买方式</h3>
                
                <!-- 选项切换 -->
                <div class="flex rounded-lg bg-gray-100 p-1 mb-5">
                    <button class="flex-1 py-2 rounded-lg font-medium text-center payment-tab active" data-tab="direct">直接购买</button>
                    <button class="flex-1 py-2 rounded-lg font-medium text-center payment-tab" data-tab="code">兑换码</button>
                </div>
                
                <!-- 直接购买选项 -->
                <div id="direct-payment" class="payment-content">
                    <h4 class="font-medium mb-2">支付方式</h4>
                    <div class="space-y-3">
                        <label class="flex items-center justify-between border p-3 rounded-lg">
                            <div class="flex items-center">
                                <input type="radio" name="payment" checked="" class="mr-3">
                                <iconify-icon icon="mdi:wechat" class="text-2xl text-green-500 mr-2"></iconify-icon>
                                微信支付
                            </div>
                            <iconify-icon icon="mdi:check-circle" class="text-green-500"></iconify-icon>
                        </label>
                        
                        <label class="flex items-center justify-between border p-3 rounded-lg">
                            <div class="flex items-center">
                                <input type="radio" name="payment" class="mr-3">
                                <iconify-icon icon="mdi:alipay" class="text-2xl text-blue-500 mr-2"></iconify-icon>
                                支付宝
                            </div>
                        </label>
                    </div>
                    
                    <button class="w-full bg-gradient-to-r from-red-600 to-red-700 text-white py-3 rounded-lg font-bold mt-6 pulse-btn">
                        立即支付 ¥3980
                    </button>
                </div>
                
                <!-- 兑换码选项 -->
                <div id="code-payment" class="payment-content hidden">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">输入兑换码</label>
                        <input type="text" id="redemption-code" placeholder="请输入VIP会员兑换码" class="block w-full bg-white border border-gray-300 rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500">
                        <p class="text-xs text-gray-500 mt-1">兑换码可通过邀请好友或参与活动获得</p>
                    </div>
                    
                    <button id="verify-code" class="w-full bg-gradient-to-r from-amber-500 to-amber-600 text-white py-3 rounded-lg font-bold">
                        验证兑换
                    </button>
                    
                    <!-- 验证成功提示（默认隐藏） -->
                    <div id="verification-success" class="hidden mt-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
                        <strong class="font-bold">验证成功!</strong>
                        <span class="block sm:inline">兑换码有效，点击下方按钮完成兑换。</span>
                    </div>
                    
                    <!-- 验证失败提示（默认隐藏） -->
                    <div id="verification-error" class="hidden mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
                        <strong class="font-bold">验证失败!</strong>
                        <span class="block sm:inline">兑换码无效或已过期，请检查后重试。</span>
                    </div>
                    
                    <button id="redeem-code" class="w-full bg-gradient-to-r from-red-600 to-red-700 text-white py-3 rounded-lg font-bold mt-4 hidden">
                        确认兑换
                    </button>
                </div>
            </div>
        </div>

        <!-- 用户问答 -->
        <div class="mt-6 mb-8">
            <h3 class="font-bold mb-3">常见问题</h3>
            <div class="space-y-4">
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <h4 class="font-medium">VIP会员有效期是多久？</h4>
                    <p class="text-sm text-gray-600 mt-2">VIP会员有效期为购买日起一年内有效。到期前我们会通过系统提醒您续费。</p>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <h4 class="font-medium">购买后可以退款吗？</h4>
                    <p class="text-sm text-gray-600 mt-2">VIP会员为虚拟商品，购买后不支持退款。请谨慎购买。</p>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <h4 class="font-medium">如何获取兑换码？</h4>
                    <p class="text-sm text-gray-600 mt-2">您可以通过邀请好友注册并充值，或参与平台举办的活动获得VIP会员兑换码。</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:home" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">评测</span>
            </a>
            <a href="#" class="flex flex-col items-center">
                <div class="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center -mt-5 shadow-lg">
                    <iconify-icon icon="mdi:water" class="text-2xl text-white"></iconify-icon>
                </div>
                <span class="text-xs mt-1 text-gray-500">线索</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-group-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">邀请</span>
            </a>
            <a href="#" class="flex flex-col items-center text-blue-600">
                <iconify-icon icon="mdi:account-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
</div>

<!-- 管理员审核放水线索页面 -->
<div class="app-container">
    <div class="fixed-section bg-white py-3 px-4 border-b border-gray-100">
        <div class="flex items-center">
            <iconify-icon icon="mdi:arrow-left" class="text-2xl text-gray-700 back-to-admin"></iconify-icon>
            <h1 class="text-lg font-bold ml-4">审核放水线索</h1>
            <div class="ml-auto flex items-center">
                <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">管理员</span>
            </div>
        </div>
    </div>

    <div class="content-flex hide-scrollbar px-4 pt-2">
        <!-- 筛选栏 -->
        <div class="flex justify-between items-center my-3">
            <div class="flex">
                <button class="font-bold mr-6 border-b-2 border-red-500 pb-2">待审核</button>
                <button class="font-medium text-gray-500 mr-6 pb-2">已通过</button>
                <button class="font-medium text-gray-500 pb-2">已拒绝</button>
            </div>
            <div class="ml-auto text-sm flex items-center text-gray-500">
                最新优先
                <iconify-icon icon="mdi:chevron-down"></iconify-icon>
            </div>
        </div>
        
        <!-- 待审核放水线索列表 -->
        <div class="mt-3 space-y-4 pb-6">
            <!-- 线索卡片1 -->
            <div class="card p-4 bg-gradient-to-r from-amber-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:cash-multiple" class="text-xl text-green-500 mr-2"></iconify-icon>
                        <span class="font-semibold">趣步多多</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:clock" class="mr-1"></iconify-icon>待审核
                        </span>
                    </div>
                </div>
                <div class="mt-3 bg-gray-50 p-3 rounded-lg text-sm">
                    <p>用户反馈：<span class="text-red-500 font-medium">放水0.8元</span>，到账速度快，今天特别活动双倍奖励！</p>
                    <p class="mt-1 text-xs text-gray-500">提交人：用户5146 [小米13] | 2025-05-13 16:37</p>
                </div>
                <div class="grid grid-cols-2 gap-3 mt-4">
                    <img src="https://placehold.co/200x300/e9ecef/495057?text=APP%E6%8F%90%E7%8E%B0%E8%AE%B0%E5%BD%95" alt="APP提现记录" class="w-full h-auto rounded-lg">
                    <img src="https://placehold.co/200x300/e9ecef/495057?text=%E5%BE%AE%E4%BF%A1%E5%88%B0%E8%B4%A6%E8%AE%B0%E5%BD%95" alt="微信到账记录" class="w-full h-auto rounded-lg">
                </div>
                <div class="flex justify-between items-center mt-4">
                    <button class="bg-green-500 text-white px-4 py-2 rounded-lg font-medium">
                        <iconify-icon icon="mdi:check" class="mr-1"></iconify-icon>通过
                    </button>
                    <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium">
                        修改后通过
                    </button>
                    <button class="bg-red-500 text-white px-4 py-2 rounded-lg font-medium">
                        <iconify-icon icon="mdi:close" class="mr-1"></iconify-icon>拒绝
                    </button>
                </div>
            </div>
            
            <!-- 线索卡片2 -->
            <div class="card p-4 bg-gradient-to-r from-purple-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:gamepad-variant" class="text-xl text-purple-500 mr-2"></iconify-icon>
                        <span class="font-semibold">游戏红包</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:clock" class="mr-1"></iconify-icon>待审核
                        </span>
                    </div>
                </div>
                <div class="mt-3 bg-gray-50 p-3 rounded-lg text-sm">
                    <p>用户反馈：<span class="text-red-500 font-medium">首充返现1.0元</span>，秒到账，玩游戏同时还能赚钱！</p>
                    <p class="mt-1 text-xs text-gray-500">提交人：用户7892 [vivo X90] | 2025-05-13 15:21</p>
                </div>
                <div class="grid grid-cols-2 gap-3 mt-4">
                    <img src="https://placehold.co/200x300/e9ecef/495057?text=APP%E6%8F%90%E7%8E%B0%E8%AE%B0%E5%BD%95" alt="APP提现记录" class="w-full h-auto rounded-lg">
                    <img src="https://placehold.co/200x300/e9ecef/495057?text=%E5%BE%AE%E4%BF%A1%E5%88%B0%E8%B4%A6%E8%AE%B0%E5%BD%95" alt="微信到账记录" class="w-full h-auto rounded-lg">
                </div>
                <div class="flex justify-between items-center mt-4">
                    <button class="bg-green-500 text-white px-4 py-2 rounded-lg font-medium">
                        <iconify-icon icon="mdi:check" class="mr-1"></iconify-icon>通过
                    </button>
                    <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium">
                        修改后通过
                    </button>
                    <button class="bg-red-500 text-white px-4 py-2 rounded-lg font-medium">
                        <iconify-icon icon="mdi:close" class="mr-1"></iconify-icon>拒绝
                    </button>
                </div>
            </div>
            
            <!-- 线索卡片3 -->
            <div class="card p-4 bg-gradient-to-r from-green-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:book-open-variant" class="text-xl text-blue-500 mr-2"></iconify-icon>
                        <span class="font-semibold">阅读赚</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:clock" class="mr-1"></iconify-icon>待审核
                        </span>
                    </div>
                </div>
                <div class="mt-3 bg-gray-50 p-3 rounded-lg text-sm">
                    <p>用户反馈：<span class="text-red-500 font-medium">连续签到奖励0.6元</span>，今天阅读文章特别多，收益高！</p>
                    <p class="mt-1 text-xs text-gray-500">提交人：用户3456 [OPPO Find X5] | 2025-05-13 14:05</p>
                </div>
                <div class="grid grid-cols-2 gap-3 mt-4">
                    <img src="https://placehold.co/200x300/e9ecef/495057?text=APP%E6%8F%90%E7%8E%B0%E8%AE%B0%E5%BD%95" alt="APP提现记录" class="w-full h-auto rounded-lg">
                    <img src="https://placehold.co/200x300/e9ecef/495057?text=%E5%BE%AE%E4%BF%A1%E5%88%B0%E8%B4%A6%E8%AE%B0%E5%BD%95" alt="微信到账记录" class="w-full h-auto rounded-lg">
                </div>
                <div class="flex justify-between items-center mt-4">
                    <button class="bg-green-500 text-white px-4 py-2 rounded-lg font-medium">
                        <iconify-icon icon="mdi:check" class="mr-1"></iconify-icon>通过
                    </button>
                    <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium">
                        修改后通过
                    </button>
                    <button class="bg-red-500 text-white px-4 py-2 rounded-lg font-medium">
                        <iconify-icon icon="mdi:close" class="mr-1"></iconify-icon>拒绝
                    </button>
                </div>
            </div>
            
            <!-- 加载更多 -->
            <div class="text-center mb-8">
                <button class="text-sm text-red-500 font-medium flex items-center justify-center mx-auto">
                    加载更多 <iconify-icon icon="mdi:chevron-down" class="ml-1"></iconify-icon>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:view-dashboard" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">管理主页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-blue-600">
                <iconify-icon icon="mdi:water" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">放水审核</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">报告审核</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-cog" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">设置</span>
            </a>
        </div>
    </div>
</div>

<!-- 管理员主页 -->
<div class="app-container">
    <div class="fixed-section bg-white py-3 px-4 border-b border-gray-100">
        <div class="flex items-center">
            <h1 class="text-lg font-bold ml-4">管理员控制台</h1>
            <div class="ml-auto flex items-center">
                <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">管理员</span>
            </div>
        </div>
    </div>

    <div class="content-flex hide-scrollbar px-4 pt-3">
        <!-- 欢迎卡片 -->
        <div class="bg-gradient-to-r from-blue-800 to-blue-900 text-white py-4 px-4 rounded-lg mb-4">
            <div class="flex items-center">
                <div class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                    <iconify-icon icon="mdi:account-cog" class="text-2xl"></iconify-icon>
                </div>
                <div class="ml-3">
                    <p class="text-lg font-bold">管理员，您好！</p>
                    <p class="text-sm opacity-80">上次登录时间: 2025-05-13 08:30</p>
                </div>
            </div>
        </div>
        
        <!-- 待审核统计卡片 -->
        <div class="grid grid-cols-2 gap-4 mb-5">
            <div class="card bg-white p-4 rounded-lg shadow-sm">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="font-bold">待审核线索</h3>
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">8个</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">较昨日</span>
                    <div class="flex items-center text-green-500 text-sm">
                        <iconify-icon icon="mdi:arrow-up"></iconify-icon>
                        <span>+2</span>
                    </div>
                </div>
                <button class="mt-3 w-full bg-blue-600 text-white py-2 rounded text-sm font-medium">
                    查看待审核线索
                </button>
            </div>
            
            <div class="card bg-white p-4 rounded-lg shadow-sm">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="font-bold">待审核报告</h3>
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">12个</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">较昨日</span>
                    <div class="flex items-center text-red-500 text-sm">
                        <iconify-icon icon="mdi:arrow-up"></iconify-icon>
                        <span>+5</span>
                    </div>
                </div>
                <button class="mt-3 w-full bg-blue-600 text-white py-2 rounded text-sm font-medium">
                    查看待审核报告
                </button>
            </div>
        </div>
        
        <!-- 平台数据统计 -->
        <div class="card bg-white p-4 rounded-lg shadow-sm mb-5">
            <h3 class="font-bold mb-3">平台数据</h3>
            <div class="grid grid-cols-3 gap-2 text-center">
                <div class="bg-gray-50 p-3 rounded">
                    <p class="text-xs text-gray-500">总用户数</p>
                    <p class="font-bold text-lg">8,546</p>
                    <p class="text-xs text-green-500">+12%</p>
                </div>
                <div class="bg-gray-50 p-3 rounded">
                    <p class="text-xs text-gray-500">总报告数</p>
                    <p class="font-bold text-lg">1,283</p>
                    <p class="text-xs text-green-500">+8%</p>
                </div>
                <div class="bg-gray-50 p-3 rounded">
                    <p class="text-xs text-gray-500">总线索数</p>
                    <p class="font-bold text-lg">2,670</p>
                    <p class="text-xs text-green-500">+15%</p>
                </div>
                <div class="bg-gray-50 p-3 rounded">
                    <p class="text-xs text-gray-500">今日新增用户</p>
                    <p class="font-bold text-lg">32</p>
                    <p class="text-xs text-red-500">-5%</p>
                </div>
                <div class="bg-gray-50 p-3 rounded">
                    <p class="text-xs text-gray-500">今日新增报告</p>
                    <p class="font-bold text-lg">18</p>
                    <p class="text-xs text-green-500">+20%</p>
                </div>
                <div class="bg-gray-50 p-3 rounded">
                    <p class="text-xs text-gray-500">今日新增线索</p>
                    <p class="font-bold text-lg">24</p>
                    <p class="text-xs text-green-500">+10%</p>
                </div>
            </div>
        </div>
        
        <!-- 最近审核历史 -->
        <div class="card bg-white p-4 rounded-lg shadow-sm mb-5">
            <div class="flex justify-between items-center mb-3">
                <h3 class="font-bold">最近审核记录</h3>
                <button class="text-blue-600 text-sm">查看全部</button>
            </div>
            
            <div class="space-y-3">
                <!-- 审核记录1 -->
                <div class="border-b border-gray-100 pb-2">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <iconify-icon icon="mdi:file-document" class="text-blue-600"></iconify-icon>
                            </div>
                            <div class="ml-2">
                                <p class="font-medium text-sm">短剧星球</p>
                                <p class="text-xs text-gray-500">评测报告</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full mr-2">已通过</span>
                            <span class="text-xs text-gray-500">10分钟前</span>
                        </div>
                    </div>
                </div>
                
                <!-- 审核记录2 -->
                <div class="border-b border-gray-100 pb-2">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <iconify-icon icon="mdi:water" class="text-blue-600"></iconify-icon>
                            </div>
                            <div class="ml-2">
                                <p class="font-medium text-sm">趣步多多</p>
                                <p class="text-xs text-gray-500">放水线索</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full mr-2">已通过</span>
                            <span class="text-xs text-gray-500">25分钟前</span>
                        </div>
                    </div>
                </div>
                
                <!-- 审核记录3 -->
                <div class="border-b border-gray-100 pb-2">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <iconify-icon icon="mdi:file-document" class="text-blue-600"></iconify-icon>
                            </div>
                            <div class="ml-2">
                                <p class="font-medium text-sm">阅读赚</p>
                                <p class="text-xs text-gray-500">评测报告</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full mr-2">已拒绝</span>
                            <span class="text-xs text-gray-500">1小时前</span>
                        </div>
                    </div>
                </div>
                
                <!-- 审核记录4 -->
                <div>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <iconify-icon icon="mdi:water" class="text-blue-600"></iconify-icon>
                            </div>
                            <div class="ml-2">
                                <p class="font-medium text-sm">金币大师</p>
                                <p class="text-xs text-gray-500">放水线索</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full mr-2">已通过</span>
                            <span class="text-xs text-gray-500">2小时前</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 快捷功能 -->
        <div class="card bg-white p-4 rounded-lg shadow-sm mb-8">
            <h3 class="font-bold mb-3">快捷功能</h3>
            <div class="grid grid-cols-2 gap-3">
                <button class="bg-gray-50 p-3 rounded-lg flex flex-col items-center justify-center">
                    <iconify-icon icon="mdi:account-plus" class="text-xl text-blue-600 mb-2"></iconify-icon>
                    <span class="text-sm">添加用户</span>
                </button>
                <button class="bg-gray-50 p-3 rounded-lg flex flex-col items-center justify-center">
                    <iconify-icon icon="mdi:plus-circle" class="text-xl text-blue-600 mb-2"></iconify-icon>
                    <span class="text-sm">添加APP</span>
                </button>
                <button class="bg-gray-50 p-3 rounded-lg flex flex-col items-center justify-center">
                    <iconify-icon icon="mdi:ticket-percent" class="text-xl text-blue-600 mb-2"></iconify-icon>
                    <span class="text-sm">生成兑换码</span>
                </button>
                <button class="bg-gray-50 p-3 rounded-lg flex flex-col items-center justify-center">
                    <iconify-icon icon="mdi:bell-ring" class="text-xl text-blue-600 mb-2"></iconify-icon>
                    <span class="text-sm">系统通知</span>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-blue-600">
                <iconify-icon icon="mdi:view-dashboard" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">管理主页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500" id="admin-water-review">
                <iconify-icon icon="mdi:water" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">放水审核</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500" id="admin-report-review">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">报告审核</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-cog" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">设置</span>
            </a>
        </div>
    </div>
</div>

<!-- 管理员审核评测报告页面 -->
<div class="app-container">
    <div class="fixed-section bg-white py-3 px-4 border-b border-gray-100">
        <div class="flex items-center">
            <iconify-icon icon="mdi:arrow-left" class="text-2xl text-gray-700 back-to-admin"></iconify-icon>
            <h1 class="text-lg font-bold ml-4">审核评测报告</h1>
            <div class="ml-auto flex items-center">
                <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">管理员</span>
            </div>
        </div>
    </div>

    <div class="content-flex hide-scrollbar px-4 pt-2">
        <!-- 筛选栏 -->
        <div class="flex justify-between items-center my-3">
            <div class="flex">
                <button class="font-bold mr-6 border-b-2 border-red-500 pb-2">待审核</button>
                <button class="font-medium text-gray-500 mr-6 pb-2">已通过</button>
                <button class="font-medium text-gray-500 pb-2">已拒绝</button>
            </div>
            <div class="ml-auto text-sm flex items-center text-gray-500">
                最新优先
                <iconify-icon icon="mdi:chevron-down"></iconify-icon>
            </div>
        </div>
        
        <!-- 待审核评测报告列表 -->
        <div class="mt-3 space-y-4 pb-6">
            <!-- 报告卡片1 -->
            <div class="card p-4 bg-gradient-to-r from-amber-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:cash-multiple" class="text-xl text-green-500 mr-2"></iconify-icon>
                        <span class="font-semibold">趣步多多</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:clock" class="mr-1"></iconify-icon>待审核
                        </span>
                    </div>
                </div>
                <div class="flex gap-2 mt-3">
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥高佣</span>
                    <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡$0.5起提</span>
                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">走路赚钱</span>
                </div>
                <div class="mt-3 bg-gray-50 p-3 rounded-lg text-sm">
                    <p class="font-medium">评测报告详情：</p>
                    <p class="mt-1">该APP为步行赚钱类型，注册送0.5元，每天走路可赚0.3-1.2元不等，提现门槛0.5元，秒到账。测试期间共完成10条广告观看，总收益2.8元，每条约0.28元。</p>
                    <p class="mt-1 text-xs text-gray-500">提交人：用户5146 [小米13] | 2025-05-13 16:37</p>
                </div>
                <div class="mt-3">
                    <h3 class="font-medium mb-2">测试图片</h3>
                    <div class="grid grid-cols-3 gap-2">
                        <img src="https://placehold.co/200x300/e9ecef/495057?text=APP%E4%B8%BB%E7%95%8C%E9%9D%A2" alt="APP主界面" class="w-full h-auto rounded-lg">
                        <img src="https://placehold.co/200x300/e9ecef/495057?text=APP%E6%8F%90%E7%8E%B0%E8%AE%B0%E5%BD%95" alt="APP提现记录" class="w-full h-auto rounded-lg">
                        <img src="https://placehold.co/200x300/e9ecef/495057?text=%E5%BE%AE%E4%BF%A1%E5%88%B0%E8%B4%A6" alt="微信到账" class="w-full h-auto rounded-lg">
                    </div>
                </div>
                <div class="mt-3">
                    <h3 class="font-medium mb-2">数据报告</h3>
                    <div class="grid grid-cols-2 gap-2 mt-2 text-center">
                        <div class="bg-gray-50 py-2 rounded">
                            <p class="text-xs text-gray-500">测试条数</p>
                            <p class="font-semibold">10条</p>
                        </div>
                        <div class="bg-gray-50 py-2 rounded">
                            <p class="text-xs text-gray-500">测试收益</p>
                            <p class="font-semibold">¥2.8</p>
                        </div>
                        <div class="bg-gray-50 py-2 rounded">
                            <p class="text-xs text-gray-500">测试时长</p>
                            <p class="font-semibold">12分钟</p>
                        </div>
                        <div class="bg-gray-50 py-2 rounded">
                            <p class="text-xs text-gray-500">测试设备</p>
                            <p class="font-semibold">小米13</p>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between items-center mt-4">
                    <button class="bg-green-500 text-white px-4 py-2 rounded-lg font-medium">
                        <iconify-icon icon="mdi:check" class="mr-1"></iconify-icon>通过
                    </button>
                    <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium">
                        修改后通过
                    </button>
                    <button class="bg-red-500 text-white px-4 py-2 rounded-lg font-medium">
                        <iconify-icon icon="mdi:close" class="mr-1"></iconify-icon>拒绝
                    </button>
                </div>
            </div>
            
            <!-- 报告卡片2 -->
            <div class="card p-4 bg-gradient-to-r from-blue-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:video" class="text-xl text-red-500 mr-2"></iconify-icon>
                        <span class="font-semibold">短剧星球</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:clock" class="mr-1"></iconify-icon>待审核
                        </span>
                    </div>
                </div>
                <div class="flex gap-2 mt-3">
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥爆款</span>
                    <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡秒提现</span>
                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">新人$1.0</span>
                </div>
                <div class="mt-3 bg-gray-50 p-3 rounded-lg text-sm">
                    <p class="font-medium">评测报告详情：</p>
                    <p class="mt-1">该APP为短视频类型，注册送1.0元，每看完一集短剧可获得0.3-0.5元，连续观看有额外奖励。提现门槛1元，一般1小时内到账。测试期间观看5集短剧，共获得收益3.5元。</p>
                    <p class="mt-1 text-xs text-gray-500">提交人：用户7892 [vivo X90] | 2025-05-13 15:21</p>
                </div>
                <div class="mt-3">
                    <h3 class="font-medium mb-2">测试图片</h3>
                    <div class="grid grid-cols-3 gap-2">
                        <img src="https://placehold.co/200x300/e9ecef/495057?text=APP%E4%B8%BB%E7%95%8C%E9%9D%A2" alt="APP主界面" class="w-full h-auto rounded-lg">
                        <img src="https://placehold.co/200x300/e9ecef/495057?text=APP%E6%8F%90%E7%8E%B0%E8%AE%B0%E5%BD%95" alt="APP提现记录" class="w-full h-auto rounded-lg">
                        <img src="https://placehold.co/200x300/e9ecef/495057?text=%E5%BE%AE%E4%BF%A1%E5%88%B0%E8%B4%A6" alt="微信到账" class="w-full h-auto rounded-lg">
                    </div>
                </div>
                <div class="mt-3">
                    <h3 class="font-medium mb-2">数据报告</h3>
                    <div class="grid grid-cols-2 gap-2 mt-2 text-center">
                        <div class="bg-gray-50 py-2 rounded">
                            <p class="text-xs text-gray-500">测试条数</p>
                            <p class="font-semibold">5条</p>
                        </div>
                        <div class="bg-gray-50 py-2 rounded">
                            <p class="text-xs text-gray-500">测试收益</p>
                            <p class="font-semibold">¥3.5</p>
                        </div>
                        <div class="bg-gray-50 py-2 rounded">
                            <p class="text-xs text-gray-500">测试时长</p>
                            <p class="font-semibold">15分钟</p>
                        </div>
                        <div class="bg-gray-50 py-2 rounded">
                            <p class="text-xs text-gray-500">测试设备</p>
                            <p class="font-semibold">vivo X90</p>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between items-center mt-4">
                    <button class="bg-green-500 text-white px-4 py-2 rounded-lg font-medium">
                        <iconify-icon icon="mdi:check" class="mr-1"></iconify-icon>通过
                    </button>
                    <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium">
                        修改后通过
                    </button>
                    <button class="bg-red-500 text-white px-4 py-2 rounded-lg font-medium">
                        <iconify-icon icon="mdi:close" class="mr-1"></iconify-icon>拒绝
                    </button>
                </div>
            </div>
            
            <!-- 报告卡片3 -->
            <div class="card p-4 bg-gradient-to-r from-red-50 to-white">
                <div class="flex justify-between">
                    <div class="flex items-center">
                        <iconify-icon icon="mdi:coin-outline" class="text-xl text-amber-500 mr-2"></iconify-icon>
                        <span class="font-semibold">金币大师</span>
                    </div>
                    <div class="flex gap-2">
                        <span class="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full flex items-center">
                            <iconify-icon icon="mdi:clock" class="mr-1"></iconify-icon>待审核
                        </span>
                    </div>
                </div>
                <div class="flex gap-2 mt-3">
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">🔥自动</span>
                    <span class="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">⚡$0.1起提</span>
                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">新人$0.25</span>
                </div>
                <div class="mt-3 bg-gray-50 p-3 rounded-lg text-sm">
                    <p class="font-medium">评测报告详情：</p>
                    <p class="mt-1">该APP为合成类小游戏，注册送0.25元，通过合成金币可赚取现金。提现门槛0.1元，微信秒到账。测试期间玩了约8分钟，共获得10次收益，总计2.5元。全程自动运行，十分轻松。</p>
                    <p class="mt-1 text-xs text-gray-500">提交人：用户3456 [华为Mate40 Pro] | 2025-05-13 14:05</p>
                </div>
                <div class="mt-3">
                    <h3 class="font-medium mb-2">测试图片</h3>
                    <div class="grid grid-cols-3 gap-2">
                        <img src="https://placehold.co/200x300/e9ecef/495057?text=APP%E4%B8%BB%E7%95%8C%E9%9D%A2" alt="APP主界面" class="w-full h-auto rounded-lg">
                        <img src="https://placehold.co/200x300/e9ecef/495057?text=APP%E6%8F%90%E7%8E%B0%E8%AE%B0%E5%BD%95" alt="APP提现记录" class="w-full h-auto rounded-lg">
                        <img src="https://placehold.co/200x300/e9ecef/495057?text=%E5%BE%AE%E4%BF%A1%E5%88%B0%E8%B4%A6" alt="微信到账" class="w-full h-auto rounded-lg">
                    </div>
                </div>
                <div class="mt-3">
                    <h3 class="font-medium mb-2">数据报告</h3>
                    <div class="grid grid-cols-2 gap-2 mt-2 text-center">
                        <div class="bg-gray-50 py-2 rounded">
                            <p class="text-xs text-gray-500">测试条数</p>
                            <p class="font-semibold">10条</p>
                        </div>
                        <div class="bg-gray-50 py-2 rounded">
                            <p class="text-xs text-gray-500">测试收益</p>
                            <p class="font-semibold">¥2.5</p>
                        </div>
                        <div class="bg-gray-50 py-2 rounded">
                            <p class="text-xs text-gray-500">测试时长</p>
                            <p class="font-semibold">8分钟</p>
                        </div>
                        <div class="bg-gray-50 py-2 rounded">
                            <p class="text-xs text-gray-500">测试设备</p>
                            <p class="font-semibold">华为Mate40 Pro</p>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between items-center mt-4">
                    <button class="bg-green-500 text-white px-4 py-2 rounded-lg font-medium">
                        <iconify-icon icon="mdi:check" class="mr-1"></iconify-icon>通过
                    </button>
                    <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium">
                        修改后通过
                    </button>
                    <button class="bg-red-500 text-white px-4 py-2 rounded-lg font-medium">
                        <iconify-icon icon="mdi:close" class="mr-1"></iconify-icon>拒绝
                    </button>
                </div>
            </div>
            
            <!-- 加载更多 -->
            <div class="text-center mb-8">
                <button class="text-sm text-red-500 font-medium flex items-center justify-center mx-auto">
                    加载更多 <iconify-icon icon="mdi:chevron-down" class="ml-1"></iconify-icon>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:view-dashboard" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">管理主页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:water" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">放水审核</span>
            </a>
            <a href="#" class="flex flex-col items-center text-blue-600">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">报告审核</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-cog" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">设置</span>
            </a>
        </div>
    </div>
</div>

<!-- 商品详情页面 -->
<div class="app-container" id="product-detail-page">
    <div class="fixed-section bg-white py-3 px-4 border-b border-gray-100">
        <div class="flex items-center">
            <iconify-icon icon="mdi:arrow-left" class="text-2xl text-gray-700 back-to-mall"></iconify-icon>
            <h1 class="text-lg font-bold ml-4">商品详情</h1>
        </div>
    </div>

    <div class="content-flex hide-scrollbar px-4 pt-4">
        <!-- 商品图片轮播 -->
        <div class="rounded-lg overflow-hidden mb-4">
            <img id="product-image" src="https://placehold.co/600x400/e9ecef/495057?text=%E5%95%86%E5%93%81%E5%9B%BE%E7%89%87" alt="商品图片" class="w-full h-auto">
        </div>
        
        <!-- 商品信息 -->
        <div class="bg-white rounded-lg p-4 shadow-sm mb-4">
            <h2 id="product-title" class="text-xl font-bold">华为Mate40 Pro</h2>
            <div class="flex items-center mt-2">
                <div class="flex items-center text-amber-400">
                    <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>
                    <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>
                    <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>
                    <iconify-icon icon="mdi:star" class="mr-1"></iconify-icon>
                    <iconify-icon icon="mdi:star-half" class="mr-1"></iconify-icon>
                </div>
                <span class="text-sm text-gray-500 ml-2">4.8分 | 已售283台</span>
            </div>
            <div class="mt-3">
                <div id="product-price" class="text-2xl font-bold text-red-600">¥3999</div>
                <div id="product-points-price" class="mt-1 text-amber-500">或 <span class="font-bold">1000积分+¥2999</span></div>
            </div>
        </div>
        
        <!-- 商品规格 -->
        <div class="bg-white rounded-lg p-4 shadow-sm mb-4">
            <h3 class="font-bold mb-3">商品规格</h3>
            <div class="space-y-2">
                <div class="flex justify-between text-sm">
                    <span class="text-gray-500">品牌</span>
                    <span id="product-brand">华为(HUAWEI)</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span class="text-gray-500">型号</span>
                    <span id="product-model">Mate40 Pro</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span class="text-gray-500">运行内存</span>
                    <span id="product-ram">8GB</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span class="text-gray-500">存储容量</span>
                    <span id="product-storage">256GB</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span class="text-gray-500">处理器</span>
                    <span id="product-cpu">麒麟9000</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span class="text-gray-500">屏幕尺寸</span>
                    <span id="product-screen">6.76英寸</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span class="text-gray-500">适用人群</span>
                    <span id="product-target">评测人员，运营人员</span>
                </div>
            </div>
        </div>
        
        <!-- 商品详情 -->
        <div class="bg-white rounded-lg p-4 shadow-sm mb-4">
            <h3 class="font-bold mb-3">商品详情</h3>
            <div class="text-sm text-gray-700">
                <p id="product-description">华为Mate40 Pro是华为公司推出的旗舰手机，搭载麒麟9000芯片，拥有超曲面环幕屏、5nm工艺、WiFi6+等先进技术。该手机在拍照、游戏性能方面表现优异，是进行APP评测的理想设备，适用于98%的评测APP，可以无卡顿运行各类应用。</p>
                <div class="mt-3 space-y-3">
                    <img src="https://placehold.co/600x400/e9ecef/495057?text=%E4%BA%A7%E5%93%81%E7%BB%86%E8%8A%82%E5%9B%BE1" alt="产品细节图" class="w-full h-auto rounded-lg">
                    <img src="https://placehold.co/600x400/e9ecef/495057?text=%E4%BA%A7%E5%93%81%E7%BB%86%E8%8A%82%E5%9B%BE2" alt="产品细节图" class="w-full h-auto rounded-lg">
                </div>
            </div>
        </div>
        
        <!-- 购买须知 -->
        <div class="bg-white rounded-lg p-4 shadow-sm mb-4">
            <h3 class="font-bold mb-3">购买须知</h3>
            <ul class="text-sm text-gray-700 space-y-2 list-disc pl-5">
                <li>积分兑换商品不支持7天无理由退货</li>
                <li>实际发货以仓库实际库存为准</li>
                <li>商品图片仅供参考，请以实物为准</li>
                <li>产品包装随厂家更新可能有所不同</li>
                <li>运费说明：积分商城商品全场包邮</li>
            </ul>
        </div>
        
        <!-- 评价 -->
        <div class="bg-white rounded-lg p-4 shadow-sm mb-12">
            <div class="flex justify-between items-center mb-3">
                <h3 class="font-bold">用户评价 (28)</h3>
                <a href="#" class="text-sm text-blue-500">查看全部</a>
            </div>
            
            <!-- 评价1 -->
            <div class="border-b border-gray-100 pb-3 mb-3">
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-gray-200 overflow-hidden flex-shrink-0">
                        <img src="https://placehold.co/40x40" alt="用户头像" class="w-full h-full object-cover">
                    </div>
                    <div class="ml-2">
                        <p class="text-sm font-medium">用户8273</p>
                        <div class="flex items-center text-amber-400 text-xs">
                            <iconify-icon icon="mdi:star" class="mr-0.5"></iconify-icon>
                            <iconify-icon icon="mdi:star" class="mr-0.5"></iconify-icon>
                            <iconify-icon icon="mdi:star" class="mr-0.5"></iconify-icon>
                            <iconify-icon icon="mdi:star" class="mr-0.5"></iconify-icon>
                            <iconify-icon icon="mdi:star" class="mr-0.5"></iconify-icon>
                        </div>
                    </div>
                    <span class="text-xs text-gray-500 ml-auto">2025-05-10</span>
                </div>
                <p class="text-sm mt-2">手机质量很好，做评测特别流畅，所有APP都能跑，特别是大型游戏毫无压力，拍照功能也很强大。</p>
            </div>
            
            <!-- 评价2 -->
            <div>
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-gray-200 overflow-hidden flex-shrink-0">
                        <img src="https://placehold.co/40x40" alt="用户头像" class="w-full h-full object-cover">
                    </div>
                    <div class="ml-2">
                        <p class="text-sm font-medium">用户6521</p>
                        <div class="flex items-center text-amber-400 text-xs">
                            <iconify-icon icon="mdi:star" class="mr-0.5"></iconify-icon>
                            <iconify-icon icon="mdi:star" class="mr-0.5"></iconify-icon>
                            <iconify-icon icon="mdi:star" class="mr-0.5"></iconify-icon>
                            <iconify-icon icon="mdi:star" class="mr-0.5"></iconify-icon>
                            <iconify-icon icon="mdi:star-outline" class="mr-0.5"></iconify-icon>
                        </div>
                    </div>
                    <span class="text-xs text-gray-500 ml-auto">2025-05-05</span>
                </div>
                <p class="text-sm mt-2">用积分抵扣了一部分价格，很划算。收到货比想象中好，运行速度快，电池续航也不错，推荐给需要做测评的朋友。</p>
            </div>
        </div>
    </div>
    
    <!-- 底部购买按钮 -->
    <div class="fixed-section bg-white border-t border-gray-200 py-3 px-4 bottom-0 left-0 right-0 flex gap-2">
        <button class="flex-1 border border-blue-500 text-blue-500 py-2 rounded-lg font-bold">加入购物车</button>
        <button class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 text-white py-2 rounded-lg font-bold">立即购买</button>
    </div>
</div>

<!-- 积分明细页面 -->
<div class="app-container">
    <div class="fixed-section bg-white py-3 px-4 border-b border-gray-100">
        <div class="flex items-center">
            <iconify-icon icon="mdi:arrow-left" class="text-2xl text-gray-700 back-to-profile"></iconify-icon>
            <h1 class="text-lg font-bold ml-4">积分明细</h1>
        </div>
    </div>

    <div class="content-flex hide-scrollbar px-4 pt-2">
        <!-- 总积分展示 -->
        <div class="bg-gradient-to-r from-blue-800 to-blue-900 text-white py-6 px-4 rounded-lg mt-3">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-sm">当前积分</p>
                    <p class="text-3xl font-bold">1250</p>
                </div>
                <div class="text-right">
                    <p class="text-xs opacity-80">本月获得</p>
                    <p class="text-xl font-medium text-amber-300">+130</p>
                </div>
            </div>
        </div>

        <!-- 筛选栏 -->
        <div class="flex justify-between items-center mt-4">
            <div class="flex">
                <button class="font-bold mr-6 border-b-2 border-blue-500 pb-2">全部</button>
                <button class="font-medium text-gray-500 mr-6 pb-2">收入</button>
                <button class="font-medium text-gray-500 pb-2">支出</button>
            </div>
            <div class="ml-auto text-sm flex items-center text-gray-500">
                最近30天
                <iconify-icon icon="mdi:chevron-down" class="ml-1"></iconify-icon>
            </div>
        </div>
        
        <!-- 积分列表 -->
        <div class="mt-3 space-y-3 pb-6">
            <!-- 日期分组 -->
            <div class="text-xs text-gray-500 pt-2">2025年5月</div>
            
            <!-- 积分记录 - 收入 -->
            <div class="card p-3 bg-white">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <iconify-icon icon="mdi:water" class="text-xl text-blue-600"></iconify-icon>
                        </div>
                        <div>
                            <p class="font-medium">提交放水线索</p>
                            <p class="text-xs text-gray-500 mt-1">2025-05-12 14:23</p>
                        </div>
                    </div>
                    <div class="text-lg font-bold text-green-500">+10</div>
                </div>
            </div>
            
            <div class="card p-3 bg-white">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <iconify-icon icon="mdi:file-document" class="text-xl text-blue-600"></iconify-icon>
                        </div>
                        <div>
                            <p class="font-medium">提交评测报告</p>
                            <p class="text-xs text-gray-500 mt-1">2025-05-10 09:23</p>
                        </div>
                    </div>
                    <div class="text-lg font-bold text-green-500">+50</div>
                </div>
            </div>
            
            <div class="card p-3 bg-white">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                            <iconify-icon icon="mdi:video" class="text-xl text-green-600"></iconify-icon>
                        </div>
                        <div>
                            <p class="font-medium">短剧星球看广告</p>
                            <p class="text-xs text-gray-500 mt-1">2025-05-07 20:45</p>
                        </div>
                    </div>
                    <div class="text-lg font-bold text-green-500">+5</div>
                </div>
            </div>
            
            <div class="card p-3 bg-white">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-3">
                            <iconify-icon icon="mdi:shopping" class="text-xl text-red-600"></iconify-icon>
                        </div>
                        <div>
                            <p class="font-medium">兑换华为Mate40 Pro</p>
                            <p class="text-xs text-gray-500 mt-1">2025-05-06 16:30</p>
                        </div>
                    </div>
                    <div class="text-lg font-bold text-red-500">-1000</div>
                </div>
            </div>
            
            <div class="card p-3 bg-white">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                            <iconify-icon icon="mdi:video" class="text-xl text-green-600"></iconify-icon>
                        </div>
                        <div>
                            <p class="font-medium">阅读赚看广告</p>
                            <p class="text-xs text-gray-500 mt-1">2025-05-05 12:10</p>
                        </div>
                    </div>
                    <div class="text-lg font-bold text-green-500">+5</div>
                </div>
            </div>
            
            <div class="card p-3 bg-white">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <iconify-icon icon="mdi:water" class="text-xl text-blue-600"></iconify-icon>
                        </div>
                        <div>
                            <p class="font-medium">提交放水线索</p>
                            <p class="text-xs text-gray-500 mt-1">2025-05-03 09:12</p>
                        </div>
                    </div>
                    <div class="text-lg font-bold text-green-500">+10</div>
                </div>
            </div>
            
            <!-- 日期分组 -->
            <div class="text-xs text-gray-500 pt-3">2025年4月</div>
            
            <div class="card p-3 bg-white">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <iconify-icon icon="mdi:file-document" class="text-xl text-blue-600"></iconify-icon>
                        </div>
                        <div>
                            <p class="font-medium">提交评测报告</p>
                            <p class="text-xs text-gray-500 mt-1">2025-04-28 16:45</p>
                        </div>
                    </div>
                    <div class="text-lg font-bold text-green-500">+50</div>
                </div>
            </div>
            
            <div class="card p-3 bg-white">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-3">
                            <iconify-icon icon="mdi:shopping" class="text-xl text-red-600"></iconify-icon>
                        </div>
                        <div>
                            <p class="font-medium">兑换VIP会员月卡</p>
                            <p class="text-xs text-gray-500 mt-1">2025-04-25 10:20</p>
                        </div>
                    </div>
                    <div class="text-lg font-bold text-red-500">-300</div>
                </div>
            </div>
            
            <div class="card p-3 bg-white">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                            <iconify-icon icon="mdi:video" class="text-xl text-green-600"></iconify-icon>
                        </div>
                        <div>
                            <p class="font-medium">金币大师看广告</p>
                            <p class="text-xs text-gray-500 mt-1">2025-04-20 19:30</p>
                        </div>
                    </div>
                    <div class="text-lg font-bold text-green-500">+5</div>
                </div>
            </div>
            
            <!-- 加载更多 -->
            <div class="text-center mb-8 pt-3">
                <button class="text-sm text-blue-500 font-medium flex items-center justify-center mx-auto">
                    加载更多 <iconify-icon icon="mdi:chevron-down" class="ml-1"></iconify-icon>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="fixed-section bg-white py-2 border-t border-gray-200 bottom-0 left-0 right-0">
        <div class="flex justify-around items-center">
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:home" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:file-document-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">评测</span>
            </a>
            <a href="#" class="flex flex-col items-center">
                <div class="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center -mt-5 shadow-lg">
                    <iconify-icon icon="mdi:water" class="text-2xl text-white"></iconify-icon>
                </div>
                <span class="text-xs mt-1 text-gray-500">线索</span>
            </a>
            <a href="#" class="flex flex-col items-center text-gray-500">
                <iconify-icon icon="mdi:account-group-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">邀请</span>
            </a>
            <a href="#" class="flex flex-col items-center text-blue-600">
                <iconify-icon icon="mdi:account-outline" class="text-xl"></iconify-icon>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
</div>

<script>
    // 管理员登录功能
    document.addEventListener('DOMContentLoaded', function() {
        // 管理员登录按钮点击事件
        const adminLoginBtn = document.getElementById('admin-login-btn');
        if (adminLoginBtn) {
            adminLoginBtn.addEventListener('click', function() {
                // 这里可以添加实际的登录逻辑，现在直接跳转到管理员主页
                // 隐藏所有页面
                const appContainers = document.querySelectorAll('.app-container');
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示管理员主页（假设是第18个app-container）
                document.querySelectorAll('.app-container')[17].style.display = 'flex';
            });
        }
        
        // 管理员主页中的待审核线索按钮点击事件
        const viewWaterReviewBtn = document.querySelector('.card .mt-3.w-full.bg-blue-600:first-child');
        if (viewWaterReviewBtn) {
            viewWaterReviewBtn.addEventListener('click', function() {
                // 隐藏所有页面
                const appContainers = document.querySelectorAll('.app-container');
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示放水线索审核页面
                document.querySelectorAll('.app-container')[16].style.display = 'flex';
            });
        }
        
        // 管理员主页中的待审核报告按钮点击事件
        const viewReportReviewBtn = document.querySelector('.card .mt-3.w-full.bg-blue-600:last-child');
        if (viewReportReviewBtn) {
            viewReportReviewBtn.addEventListener('click', function() {
                // 隐藏所有页面
                const appContainers = document.querySelectorAll('.app-container');
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示评测报告审核页面
                document.querySelectorAll('.app-container')[17].style.display = 'flex';
            });
        }
        
        // 管理员底部导航栏的放水审核按钮点击事件
        const adminWaterReviewBtn = document.getElementById('admin-water-review');
        if (adminWaterReviewBtn) {
            adminWaterReviewBtn.addEventListener('click', function() {
                // 隐藏所有页面
                const appContainers = document.querySelectorAll('.app-container');
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示放水线索审核页面
                document.querySelectorAll('.app-container')[16].style.display = 'flex';
            });
        }
        
        // 管理员底部导航栏的报告审核按钮点击事件
        const adminReportReviewBtn = document.getElementById('admin-report-review');
        if (adminReportReviewBtn) {
            adminReportReviewBtn.addEventListener('click', function() {
                // 隐藏所有页面
                const appContainers = document.querySelectorAll('.app-container');
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示评测报告审核页面
                document.querySelectorAll('.app-container')[17].style.display = 'flex';
            });
        }

        // 商品详情页功能
        // 定义产品数据
        const products = {
            1: {
                title: "华为Mate40 Pro",
                price: "¥3999",
                pointsPrice: "1000积分+¥2999",
                image: "https://placehold.co/600x400/e9ecef/495057?text=华为Mate40+Pro",
                brand: "华为(HUAWEI)",
                model: "Mate40 Pro",
                ram: "8GB",
                storage: "256GB",
                cpu: "麒麟9000",
                screen: "6.76英寸",
                target: "评测人员，运营人员",
                description: "华为Mate40 Pro是华为公司推出的旗舰手机，搭载麒麟9000芯片，拥有超曲面环幕屏、5nm工艺、WiFi6+等先进技术。该手机在拍照、游戏性能方面表现优异，是进行APP评测的理想设备，适用于98%的评测APP，可以无卡顿运行各类应用。"
            },
            2: {
                title: "iPhone 15 Pro",
                price: "¥7999",
                pointsPrice: "2000积分+¥5999",
                image: "https://placehold.co/600x400/e9ecef/495057?text=iPhone+15+Pro",
                brand: "Apple",
                model: "iPhone 15 Pro",
                ram: "6GB",
                storage: "256GB",
                cpu: "A17 Pro",
                screen: "6.1英寸",
                target: "评测人员，内容创作者",
                description: "iPhone 15 Pro采用钛金属边框设计，搭载A17 Pro芯片，提供出色的性能和能效。强大的相机系统和优化的iOS系统使其成为完美的评测设备，适用于95%的评测APP，尤其适合对视频录制和照片质量有高要求的用户。"
            },
            3: {
                title: "小米13",
                price: "¥3499",
                pointsPrice: "仅支持现金购买",
                image: "https://placehold.co/600x400/e9ecef/495057?text=小米13",
                brand: "小米",
                model: "小米13",
                ram: "8GB",
                storage: "128GB",
                cpu: "骁龙8 Gen 2",
                screen: "6.36英寸",
                target: "评测人员，普通用户",
                description: "小米13采用高通骁龙8 Gen 2处理器，提供强大的性能表现，搭配徕卡专业光学镜头，拍照体验出色。小米13轻薄便携，是进行中端APP评测的理想选择，适用于90%的评测APP，性价比高。"
            },
            4: {
                title: "OPPO Find X5",
                price: "¥3299",
                pointsPrice: "仅支持现金购买",
                image: "https://placehold.co/600x400/e9ecef/495057?text=OPPO+Find+X5",
                brand: "OPPO",
                model: "Find X5",
                ram: "12GB",
                storage: "256GB",
                cpu: "骁龙888",
                screen: "6.55英寸",
                target: "评测人员，影像爱好者",
                description: "OPPO Find X5采用骁龙888处理器，配备哈苏影像系统，拥有出色的拍摄能力和长续航。设备性能稳定，适合长时间运行各类应用，适用于88%的评测APP，尤其在影像类APP表现优异。"
            },
            5: {
                title: "vivo X90",
                price: "¥3199",
                pointsPrice: "仅支持现金购买",
                image: "https://placehold.co/600x400/e9ecef/495057?text=vivo+X90",
                brand: "vivo",
                model: "X90",
                ram: "8GB",
                storage: "256GB",
                cpu: "天玑9200",
                screen: "6.78英寸",
                target: "评测人员，游戏玩家",
                description: "vivo X90搭载天玑9200处理器，配备蔡司光学镜头，提供强大的游戏性能和拍照体验。具有出色的散热系统和高刷新率屏幕，适合进行游戏类APP测评，适用于85%的评测APP。"
            },
            6: {
                title: "华为Mate30 5G",
                price: "¥2999",
                pointsPrice: "仅支持现金购买",
                image: "https://placehold.co/600x400/e9ecef/495057?text=华为Mate30+5G",
                brand: "华为(HUAWEI)",
                model: "Mate30 5G",
                ram: "6GB",
                storage: "128GB",
                cpu: "麒麟990",
                screen: "6.62英寸",
                target: "评测人员，预算有限用户",
                description: "华为Mate30 5G搭载麒麟990处理器，支持5G网络，提供稳定的性能表现。虽然是较早的机型，但仍能满足大部分APP的测评需求，是预算有限用户的理想选择，适用于80%的评测APP。"
            }
        };

        // 商品详情页返回按钮
        const backToMallBtn = document.querySelector('.back-to-mall');
        if (backToMallBtn) {
            backToMallBtn.addEventListener('click', function() {
                // 隐藏所有页面
                const appContainers = document.querySelectorAll('.app-container');
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示商城页面
                document.querySelectorAll('.app-container')[9].style.display = 'flex';
            });
        }

        // 商品详情按钮点击事件
        const productDetailBtns = document.querySelectorAll('.product-detail-btn');
        productDetailBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.getAttribute('data-product-id');
                
                // 更新商品详情页内容
                if (products[productId]) {
                    const product = products[productId];
                    document.getElementById('product-image').src = product.image;
                    document.getElementById('product-title').textContent = product.title;
                    document.getElementById('product-price').textContent = product.price;
                    document.getElementById('product-points-price').innerHTML = '或 <span class="font-bold">' + product.pointsPrice + '</span>';
                    document.getElementById('product-brand').textContent = product.brand;
                    document.getElementById('product-model').textContent = product.model;
                    document.getElementById('product-ram').textContent = product.ram;
                    document.getElementById('product-storage').textContent = product.storage;
                    document.getElementById('product-cpu').textContent = product.cpu;
                    document.getElementById('product-screen').textContent = product.screen;
                    document.getElementById('product-target').textContent = product.target;
                    document.getElementById('product-description').textContent = product.description;
                    
                    // 隐藏所有页面
                    const appContainers = document.querySelectorAll('.app-container');
                    appContainers.forEach(container => {
                        container.style.display = 'none';
                    });
                    // 显示商品详情页
                    document.getElementById('product-detail-page').style.display = 'flex';
                }
            });
        });
    });

    // 添加VIP购买页面的倒计时功能
    function startCountdown() {
        const countdownElement = document.getElementById('countdown');
        if (!countdownElement) return;
        
        // 设置倒计时为24小时
        let hours = 23;
        let minutes = 59;
        let seconds = 59;
        
        const countdownInterval = setInterval(() => {
            seconds--;
            
            if (seconds < 0) {
                seconds = 59;
                minutes--;
            }
            
            if (minutes < 0) {
                minutes = 59;
                hours--;
            }
            
            if (hours < 0) {
                clearInterval(countdownInterval);
                countdownElement.textContent = "已结束";
                return;
            }
            
            countdownElement.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }
    
    // 处理VIP购买页面的选项卡切换
    function setupPaymentTabs() {
        const tabs = document.querySelectorAll('.payment-tab');
        const contents = document.querySelectorAll('.payment-content');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // 移除所有active类
                tabs.forEach(t => t.classList.remove('active'));
                contents.forEach(c => c.classList.add('hidden'));
                
                // 添加active类到点击的选项卡
                tab.classList.add('active');
                
                // 显示相应的内容
                const targetId = `${tab.dataset.tab}-payment`;
                document.getElementById(targetId).classList.remove('hidden');
            });
        });
    }
    
    // 处理兑换码验证
    function setupCodeVerification() {
        const verifyButton = document.getElementById('verify-code');
        const redeemButton = document.getElementById('redeem-code');
        const successMessage = document.getElementById('verification-success');
        const errorMessage = document.getElementById('verification-error');
        const codeInput = document.getElementById('redemption-code');
        
        if (!verifyButton) return;
        
        verifyButton.addEventListener('click', () => {
            const code = codeInput.value.trim();
            
            if (!code) {
                // 显示错误信息
                errorMessage.classList.remove('hidden');
                successMessage.classList.add('hidden');
                redeemButton.classList.add('hidden');
                return;
            }
            
            // 模拟验证过程
            if (code === 'VIP2025' || code === 'TESTVIP') {
                // 验证成功
                successMessage.classList.remove('hidden');
                errorMessage.classList.add('hidden');
                redeemButton.classList.remove('hidden');
                verifyButton.classList.add('hidden');
            } else {
                // 验证失败
                errorMessage.classList.remove('hidden');
                successMessage.classList.add('hidden');
                redeemButton.classList.add('hidden');
            }
        });
        
        // 如果有输入兑换码，重置验证按钮
        if (codeInput) {
            codeInput.addEventListener('input', () => {
                verifyButton.classList.remove('hidden');
                redeemButton.classList.add('hidden');
                successMessage.classList.add('hidden');
                errorMessage.classList.add('hidden');
            });
        }
        
        // 处理确认兑换按钮点击
        if (redeemButton) {
            redeemButton.addEventListener('click', () => {
                alert('兑换成功！VIP会员已激活，有效期1年。');
                // 这里可以添加跳转到会员中心页面的逻辑
            });
        }
    }

    // 页面导航逻辑
    document.addEventListener('DOMContentLoaded', function() {
        // 获取所有页面容器
        const appContainers = document.querySelectorAll('.app-container');
        
        // 导航按钮点击事件
        const navHomeBtn = document.getElementById('nav-home-btn');
        const navEvaluationBtn = document.getElementById('nav-evaluation-btn');
        const navClueBtn = document.getElementById('nav-clue-btn');
        const navInviteBtn = document.getElementById('nav-invite-btn');
        const navProfileBtn = document.getElementById('nav-profile-btn');
        
        // 设置导航按钮的点击事件
        if (navHomeBtn) {
            navHomeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                // 隐藏所有页面
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示首页
                document.querySelectorAll('.app-container')[1].style.display = 'flex';
                // 更新底部导航样式
                updateNavStyle(this);
            });
        }
        
        if (navEvaluationBtn) {
            navEvaluationBtn.addEventListener('click', function(e) {
                e.preventDefault();
                // 隐藏所有页面
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示评测页面
                document.querySelectorAll('.app-container')[2].style.display = 'flex';
                // 更新底部导航样式
                updateNavStyle(this);
            });
        }
        
        if (navClueBtn) {
            navClueBtn.addEventListener('click', function(e) {
                e.preventDefault();
                // 隐藏所有页面
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示线索页面
                document.querySelectorAll('.app-container')[5].style.display = 'flex';
                // 更新底部导航样式
                updateNavStyle(this);
            });
        }
        
        if (navInviteBtn) {
            navInviteBtn.addEventListener('click', function(e) {
                e.preventDefault();
                // 隐藏所有页面
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示邀请页面
                document.querySelectorAll('.app-container')[13].style.display = 'flex';
                // 更新底部导航样式
                updateNavStyle(this);
            });
        }
        
        if (navProfileBtn) {
            navProfileBtn.addEventListener('click', function(e) {
                e.preventDefault();
                // 隐藏所有页面
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示个人页面
                document.querySelectorAll('.app-container')[1].style.display = 'flex';
                // 更新底部导航样式
                updateNavStyle(this);
            });
        }
        
        // 更新导航栏样式
        function updateNavStyle(activeBtn) {
            const allNavBtns = document.querySelectorAll('.fixed-section .flex.justify-around a');
            allNavBtns.forEach(btn => {
                if (btn === activeBtn) {
                    btn.classList.remove('text-gray-500');
                    btn.classList.add('text-blue-600');
                } else if (!btn.querySelector('.bg-blue-600')) {
                    btn.classList.add('text-gray-500');
                    btn.classList.remove('text-blue-600');
                }
            });
        }
        
        // 测试手机链接点击事件
        const testPhoneLink = document.getElementById('test-phone-link');
        if (testPhoneLink) {
            testPhoneLink.addEventListener('click', function(e) {
                e.preventDefault();
                // 隐藏所有页面
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示积分商城页面
                document.querySelectorAll('.app-container')[9].style.display = 'flex';
            });
        }
        
        // 非会员点击评测或线索
        const nonVipLinks = document.querySelectorAll('.non-vip-link');
        nonVipLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                // 显示VIP支付弹窗
                document.getElementById('vip-payment-modal').style.display = 'flex';
            });
        });
        
        // 监听兑换码支付选项
        const paymentOptions = document.querySelectorAll('input[name="payment"]');
        const redemptionCodeInput = document.getElementById('redemption-code-input');
        if (paymentOptions && redemptionCodeInput) {
            paymentOptions.forEach(option => {
                option.addEventListener('change', function() {
                    if (this.nextElementSibling.querySelector('iconify-icon').getAttribute('icon') === 'mdi:ticket-percent') {
                        redemptionCodeInput.style.display = 'block';
                    } else {
                        redemptionCodeInput.style.display = 'none';
                    }
                });
            });
        }
        
        // 购买同款设备链接点击事件
        const buySameDeviceBtn = document.getElementById('buy-same-device');
        if (buySameDeviceBtn) {
            buySameDeviceBtn.addEventListener('click', function(e) {
                e.preventDefault();
                // 隐藏所有页面
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示积分商城页面
                document.querySelectorAll('.app-container')[9].style.display = 'flex';
            });
        }
        
        // VIP开通按钮点击事件
        const vipBtn = document.getElementById('vip-btn');
        if (vipBtn) {
            vipBtn.addEventListener('click', function() {
                // 隐藏所有页面
                const appContainers = document.querySelectorAll('.app-container');
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示VIP购买页面
                document.querySelectorAll('.app-container')[14].style.display = 'flex'; // 假设VIP购买页是第15个app-container
            });
        }
        
        // 关闭VIP支付弹窗
        const closeModalBtn = document.querySelector('.close-modal');
        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', function() {
                document.getElementById('vip-payment-modal').style.display = 'none';
            });
        }
        
        // 手机购买页面返回按钮
        const phonePageBackBtn = document.querySelectorAll('.app-container')[9].querySelector('.back-btn');
        if (phonePageBackBtn) {
            phonePageBackBtn.addEventListener('click', function() {
                // 隐藏所有页面
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示首页
                document.querySelectorAll('.app-container')[1].style.display = 'flex';
            });
        }
        // VIP码兑换按钮
        const vipCodeRedemptionBtn = document.getElementById('vip-code-redemption');
        if (vipCodeRedemptionBtn) {
            vipCodeRedemptionBtn.addEventListener('click', function() {
                // 隐藏所有页面
                const appContainers = document.querySelectorAll('.app-container');
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示VIP购买页面，并默认切换到兑换码选项
                const vipPurchasePage = document.querySelectorAll('.app-container')[14]; // 假设VIP购买页是第15个app-container
                if (vipPurchasePage) {
                    vipPurchasePage.style.display = 'flex';
                    const codeTab = vipPurchasePage.querySelector('[data-tab="code"]');
                    if (codeTab) {
                        codeTab.click();
                    }
                }
            });
        }
        
        // 提交放水线索按钮
        const submitLeakClueBtn = document.getElementById('submit-leak-clue-btn');
        if (submitLeakClueBtn) {
            submitLeakClueBtn.addEventListener('click', function() {
                // 隐藏所有页面
                const appContainers = document.querySelectorAll('.app-container');
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示提交放水线索页面
                document.querySelectorAll('.app-container')[7].style.display = 'flex';
            });
        }
        
        // 提交评测报告按钮
        const submitReportBtn = document.querySelector('.fixed-section button.w-full');
        if (submitReportBtn) {
            submitReportBtn.addEventListener('click', function() {
                // 隐藏所有页面
                const appContainers = document.querySelectorAll('.app-container');
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示提交评测报告页面
                document.querySelectorAll('.app-container')[6].style.display = 'flex';
            });
        }
        
        // 返回按钮
        const backButtons = document.querySelectorAll('iconify-icon[icon="mdi:arrow-left"]');
        backButtons.forEach(button => {
            button.addEventListener('click', function() {
                // 检查是否为管理员页面的返回按钮
                if (button.classList.contains('back-to-admin')) {
                    // 返回管理员主页（这里暂时返回到评测列表页）
                    const appContainers = document.querySelectorAll('.app-container');
                    appContainers.forEach(container => {
                        container.style.display = 'none';
                    });
                    // 显示评测列表页
                    document.querySelectorAll('.app-container')[3].style.display = 'flex';
                } else {
                    // 返回评测列表页
                    const appContainers = document.querySelectorAll('.app-container');
                    appContainers.forEach(container => {
                        container.style.display = 'none';
                    });
                    // 显示评测列表页
                    document.querySelectorAll('.app-container')[3].style.display = 'flex';
                }
            });
        });
        
        // 放水线索页中的提交按钮
        const leakClueSubmitBtn = document.querySelector('.app-container:nth-child(6) .sticky button');
        if (leakClueSubmitBtn) {
            leakClueSubmitBtn.addEventListener('click', function() {
                // 隐藏所有页面
                const appContainers = document.querySelectorAll('.app-container');
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                // 显示提交放水线索页面
                document.querySelectorAll('.app-container')[6].style.display = 'flex';
            });
        }

        // 管理员页面底部导航栏事件
        const adminNavButtons = document.querySelectorAll('.app-container:nth-child(16) .fixed-section a, .app-container:nth-child(17) .fixed-section a');
        adminNavButtons.forEach((button, index) => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 获取当前点击的项目类型
                const type = this.querySelector('span').textContent.trim();
                
                // 隐藏所有页面
                const appContainers = document.querySelectorAll('.app-container');
                appContainers.forEach(container => {
                    container.style.display = 'none';
                });
                
                // 根据类型显示对应页面
                if (type === '放水审核') {
                    document.querySelectorAll('.app-container')[16].style.display = 'flex';
                } else if (type === '报告审核') {
                    document.querySelectorAll('.app-container')[17].style.display = 'flex';
                } else {
                    // 管理主页默认显示放水审核
                    document.querySelectorAll('.app-container')[16].style.display = 'flex';
                }
                
                // 更新当前导航栏的选中状态
                adminNavButtons.forEach(btn => {
                    btn.classList.remove('text-blue-600');
                    btn.classList.add('text-gray-500');
                });
                this.classList.remove('text-gray-500');
                this.classList.add('text-blue-600');
            });
        });
    });

    // 数字动画效果
    function animateCountUp() {
        const countUpElements = document.querySelectorAll('.count-up');
        
        countUpElements.forEach(element => {
            const target = parseInt(element.getAttribute('data-count'));
            const duration = 1500; // 动画持续时间（毫秒）
            const startTime = performance.now();
            const startValue = 0;
            
            function updateCount(currentTime) {
                const elapsedTime = currentTime - startTime;
                
                if (elapsedTime < duration) {
                    const progress = elapsedTime / duration;
                    const currentCount = Math.floor(progress * target);
                    element.textContent = currentCount.toLocaleString();
                    requestAnimationFrame(updateCount);
                } else {
                    element.textContent = target.toLocaleString();
                }
            }
            
            requestAnimationFrame(updateCount);
        });
    }
    
    // 在页面显示时启动计数动画
    const navInviteBtn = document.getElementById('nav-invite-btn');
    if (navInviteBtn) {
        navInviteBtn.addEventListener('click', function() {
            setTimeout(animateCountUp, 300);
        });
    }
    
    // ECharts初始化
    const chartDom = document.getElementById('chart');
    if (chartDom) {
        const chart = echarts.init(chartDom);
        
        const option = {
        tooltip: { trigger: 'axis' },
        grid: { top: 10, bottom: 20, left: 30, right: 20 },
        xAxis: {
            type: 'category',
            name: '条数',
            data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'],
            axisLine: { show: false },
            axisTick: { show: false }
        },
        yAxis: {
            type: 'value',
            name: '收益(元)',
            axisLine: { show: false },
            splitLine: { lineStyle: { color: '#eee' } }
        },
        series: [{
            name: '收益',
            type: 'line',
            smooth: true,
            symbol: 'emptyCircle',
            symbolSize: 8,
            itemStyle: { color: '#3B82F6' },
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
                    { offset: 1, color: 'rgba(59, 130, 246, 0.03)' }
                ])
            },
            data: [2.2, 2.5, 2.3, 2.7, 2.4, 2.8, 2.1, 2.6, 2.9, 2.5]
        }]
    };
    chart.setOption(option);
</script>

</body></html>
# AuditService 审核服务使用说明

## 概述

AuditService 是 CitUI 系统的内容审核服务，提供了完整的内容审核流程，包括自动审核、人工审核、审核日志记录等功能。

## 主要功能

### 1. 内容提交审核

```php
use App\Services\Citui\AuditService;

$auditService = new AuditService();

// 提交评测报告审核
$result = $auditService->submitForAudit(
    'evaluation_report',  // 内容类型
    $reportId,           // 内容ID
    $userId              // 提交者ID
);

// 返回结果
[
    'audit_id' => 123,
    'status' => 'pending',
    'auto_audit_result' => 'pass',
    'manual_audit_required' => false,
    'estimated_completion' => '2024-01-01 12:00:00'
]
```

### 2. 人工审核处理

```php
// 审核通过
$result = $auditService->processAudit(
    $auditId,           // 审核ID
    'pass',             // 审核结果：pass/reject
    '内容质量良好',      // 审核原因
    $auditorId          // 审核员ID
);

// 审核拒绝
$result = $auditService->processAudit(
    $auditId,
    'reject',
    '内容包含不当信息',
    $auditorId
);
```

### 3. 批量审核

```php
$results = $auditService->batchAudit(
    [123, 124, 125],    // 审核ID数组
    'pass',             // 批量审核结果
    '批量通过',         // 审核原因
    $auditorId          // 审核员ID
);

// 返回结果
[
    'success' => [123, 124, 125],  // 成功的审核ID
    'failed' => []                 // 失败的审核信息
]
```

### 4. 获取审核统计

```php
$statistics = $auditService->getAuditStatistics([
    'content_type' => 'evaluation_report',
    'start_date' => '2024-01-01',
    'end_date' => '2024-01-31'
]);

// 返回统计数据
[
    'total' => 100,
    'pending' => 10,
    'reviewing' => 5,
    'passed' => 70,
    'rejected' => 15,
    'timeout' => 0,
    'auto_audit_rate' => 85.5,
    'avg_audit_time' => 2.5,
    'pass_rate' => 82.4
]
```

### 5. 处理超时审核

```php
// 处理所有超时的审核记录
$timeoutCount = $auditService->handleTimeoutAudits();
echo "处理了 {$timeoutCount} 个超时审核";
```

## 自动审核规则

### 敏感词检测

系统会根据审核规则中配置的敏感词进行自动检测：

```json
{
    "auto_audit_keywords": [
        {"word": "垃圾", "weight": "medium"},
        {"word": "骗子", "weight": "high"},
        {"word": "测试", "weight": "low"}
    ]
}
```

权重对应的分数：
- `high`: 80分
- `medium`: 50分  
- `low`: 20分

### 内容长度检测

- 内容过短（<10字符）：+30分
- 内容过长（>5000字符）：+20分

### 重复内容检测

- 检测到重复或相似内容：+40分

### 用户信誉检测

- 用户最近7天内有3次以上被拒绝：+30分
- 新注册用户（1天内）：+20分

### 审核分数阈值

- 分数 ≤ `pass_score_threshold`：自动通过
- 分数 ≥ `reject_score_threshold`：自动拒绝
- 分数在两者之间：需要人工审核

## API 接口使用

### 管理员审核接口

```bash
# 获取审核列表
GET /citui/audit?content_type=evaluation_report&audit_status=pending

# 获取审核详情
GET /citui/audit/123

# 处理审核
POST /citui/audit/123/process
{
    "result": "pass",
    "reason": "内容质量良好"
}

# 批量审核
POST /citui/audit/batch-process
{
    "audit_ids": [123, 124, 125],
    "result": "pass",
    "reason": "批量通过"
}

# 获取审核统计
GET /citui/audit/statistics/overview?content_type=evaluation_report

# 处理超时审核
POST /citui/audit/handle-timeouts
```

### 内容提交审核接口

```bash
POST /citui/audit/submit
{
    "content_type": "evaluation_report",
    "content_id": 456,
    "submitter_id": 789
}
```

## 定时任务配置

在 `app/Console/Kernel.php` 中添加定时任务：

```php
protected function schedule(Schedule $schedule)
{
    // 每小时处理一次超时审核
    $schedule->command('citui:handle-timeout-audits')
             ->hourly()
             ->withoutOverlapping();
}
```

或者使用 cron 直接调用：

```bash
# 每小时执行一次
0 * * * * php /path/to/artisan citui:handle-timeout-audits
```

## 事件和通知

### 审核结果通知

当审核完成时，系统会自动：

1. 更新内容状态
2. 发放或扣除积分
3. 记录审核日志
4. 发送通知（需要实现具体的通知逻辑）

### 积分奖励规则

- 审核通过：发放 `reward_points` 积分
- 审核拒绝：扣除 `penalty_points` 积分

## 扩展开发

### 添加新的内容类型

1. 在 `AuditService` 的 `$contentTypeMap` 中添加映射：

```php
protected array $contentTypeMap = [
    'evaluation_report' => EvaluationReport::class,
    'water_clue' => WaterClue::class,
    'new_content_type' => NewContentModel::class  // 新增
];
```

2. 确保新的模型实现 `updateAuditStatus` 方法：

```php
public function updateAuditStatus(string $status, ?string $reason = null): bool
{
    // 实现状态更新逻辑
}
```

### 自定义审核规则

可以通过继承 `AuditService` 并重写相关方法来实现自定义审核逻辑：

```php
class CustomAuditService extends AuditService
{
    protected function performAutoAudit(Model $content, AuditRule $rule): array
    {
        // 自定义自动审核逻辑
        $result = parent::performAutoAudit($content, $rule);
        
        // 添加额外的检测逻辑
        // ...
        
        return $result;
    }
}
```

## 注意事项

1. **权限控制**：确保只有具有 `content_audit` 权限的管理员才能执行审核操作
2. **事务处理**：所有审核操作都在数据库事务中执行，确保数据一致性
3. **日志记录**：所有审核操作都会记录详细的日志，便于追踪和审计
4. **缓存管理**：审核统计数据会被缓存，注意及时清理相关缓存
5. **异常处理**：所有方法都包含完整的异常处理，确保系统稳定性

## 性能优化建议

1. **索引优化**：为审核相关表添加合适的数据库索引
2. **分页查询**：大量数据时使用分页查询避免内存溢出
3. **异步处理**：考虑将审核通知等操作放入队列异步处理
4. **缓存策略**：合理使用缓存减少数据库查询
5. **定期清理**：定期清理过期的审核日志和统计数据
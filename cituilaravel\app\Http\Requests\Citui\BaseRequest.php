<?php
declare(strict_types=1);

namespace App\Http\Requests\Citui;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

abstract class BaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    
    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 422,
                'code' => 0,
                'msg' => '数据验证失败',
                'data' => $validator->errors()
            ], 422)
        );
    }
    
    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'required' => ':attribute 字段是必填的',
            'string' => ':attribute 必须是字符串',
            'integer' => ':attribute 必须是整数',
            'email' => ':attribute 必须是有效的邮箱地址',
            'phone' => ':attribute 必须是有效的手机号码',
            'max' => ':attribute 不能超过 :max 个字符',
            'min' => ':attribute 不能少于 :min 个字符',
            'unique' => ':attribute 已经存在',
            'exists' => ':attribute 不存在',
            'image' => ':attribute 必须是图片文件',
            'mimes' => ':attribute 文件类型不支持',
            'max_file_size' => ':attribute 文件大小不能超过 :max KB'
        ];
    }
    
    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'phone' => '手机号',
            'password' => '密码',
            'nickname' => '昵称',
            'real_name' => '真实姓名',
            'avatar' => '头像',
            'app_id' => 'APP ID',
            'report_title' => '报告标题',
            'report_content' => '报告内容',
            'rating' => '评分',
            'clue_title' => '线索标题',
            'clue_content' => '线索内容',
            'screenshots' => '截图'
        ];
    }
}
<?php
declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Wap\System;

use App\Http\Controllers\Api\Controller;
use App\Service\System\CityService;


class CityController extends Controller
{
    protected CityService $cityService;

    public function __construct(CityService $cityService)
    {
        $this->cityService = $cityService;
        parent::__construct();
    }

    public function cityInfoByCityCode(string $cityCode)
    {
        return $this->success($this->cityService->cityInfoByCityCode($cityCode));
    }

    public function cityInfoByCityId(int $cityId)
    {
        return $this->success($this->cityService->cityInfoByCityId($cityId));
    }

    public function getCityByLongLatNew()
    {
        return $this->success($this->cityService->getCityByLongLatNew());
    }

    public function provinceList()
    {
        return $this->success($this->cityService->provinceList());
    }

    public function cityListByPid(int $pid=0)
    {
        return $this->success($this->cityService->cityListByPid($pid));
    }

    public function getCityByLongLat()
    {
        return $this->success($this->cityService->getCityByLongLat());
    }

    public function areaListByLevelType(int $levelType=-1){
        if($levelType<0){
            $levelType = 2;//默认值
        }
        return $this->success($this->cityService->areaListByLevelType($levelType));
    }

    public function __call($method, $parameters)
    {
        return $this->success(call_user_func_array([$this->cityService, $method], $parameters));
    }

}

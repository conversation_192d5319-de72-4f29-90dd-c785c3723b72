<?php

// 配置参数
$concurrent = 5; // 并发数改小一点，便于观察
$totalRequests = 5; // 总请求数
$url = 'http://chengyu.test.com/api/Withdrawal/WithdrawalAdd';

// 存储所有请求的结果
$results = [];
$startTime = microtime(true);

// 创建批次请求
for ($batch = 0; $batch < ceil($totalRequests / $concurrent); $batch++) {
    // 创建curl_multi句柄
    $mh = curl_multi_init();
    $handles = [];
    $requestTimes = []; // 记录每个请求的开始时间
    
    // 创建当前批次的请求
    $batchSize = min($concurrent, $totalRequests - $batch * $concurrent);
    for ($i = 0; $i < $batchSize; $i++) {
        $ch = curl_init();
        
        // 设置请求参数
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        
        // 设置请求头
        $headers = [
            'User-Agent: Apifox/1.0.0 (https://apifox.com)',
            'Authorization: Bearer ***********:/NrbjEldIHQh+kelMzvf3k5vsRd8wf8OWapn5WFYabnrKLy5ZLzhQMahTYVK8OPz',
            'Accept: */*',
            'Host: chengyu.test.com',
            'Connection: keep-alive'
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        
        // 构造表单数据
        $postData = [
            'money' => '1'  // 直接使用数组形式
        ];
        
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        
        // 添加到multi句柄
        curl_multi_add_handle($mh, $ch);
        $handles[] = $ch;
        $requestTimes[$i] = microtime(true); // 记录请求开始时间
    }
    
    // 执行批次请求
    $active = null;
    do {
        $mrc = curl_multi_exec($mh, $active);
    } while ($mrc == CURLM_CALL_MULTI_PERFORM);
    
    while ($active && $mrc == CURLM_OK) {
        if (curl_multi_select($mh) != -1) {
            do {
                $mrc = curl_multi_exec($mh, $active);
            } while ($mrc == CURLM_CALL_MULTI_PERFORM);
        }
    }
    
    // 获取结果
    foreach ($handles as $i => $ch) {
        $response = curl_multi_getcontent($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $endTime = microtime(true);
        
        // 解析响应JSON
        $responseData = json_decode($response, true);
        $status = isset($responseData['status']) ? $responseData['status'] : 'unknown';
        $message = isset($responseData['message']) ? $responseData['message'] : 'No message';
        
        $results[] = [
            'request_num' => $batch * $concurrent + $i + 1,
            'http_code' => $httpCode,
            'status' => $status,
            'message' => $message,
            'time_taken' => round(($endTime - $requestTimes[$i]) * 1000, 2), // 毫秒
            'raw_response' => $response
        ];
        
        curl_multi_remove_handle($mh, $ch);
        curl_close($ch);
    }
    
    curl_multi_close($mh);
}

$endTime = microtime(true);
$totalTime = $endTime - $startTime;

// 输出结果
echo "\n============= 并发测试结果 =============\n";
echo "总请求数: {$totalRequests}\n";
echo "并发数: {$concurrent}\n";
echo "总耗时: " . round($totalTime * 1000, 2) . " ms\n\n";

$successCount = 0;
$lockCount = 0;
$otherErrorCount = 0;

foreach ($results as $result) {
    echo "请求 #{$result['request_num']} (" . $result['time_taken'] . " ms)\n";
    echo "HTTP状态码: {$result['http_code']}\n";
    echo "状态: {$result['status']}\n";
    echo "消息: {$result['message']}\n";
    
    // 统计不同类型的响应
    if ($result['http_code'] == 200 && $result['status'] == 0) {
        $successCount++;
    } elseif (strpos(strtolower($result['message']), 'lock') !== false) {
        $lockCount++;
    } else {
        $otherErrorCount++;
    }
    
    echo "------------------------\n";
}

echo "\n============= 统计信息 =============\n";
echo "成功请求: {$successCount}\n";
echo "被锁阻止: {$lockCount}\n";
echo "其他错误: {$otherErrorCount}\n";
echo "====================================\n"; 